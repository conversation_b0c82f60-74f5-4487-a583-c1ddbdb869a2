import { TikTokApiResponse, TikTokApiConfig, TikTokApiQuery, TikTokProduct, TikTokUserInfoResponse, TikTokPostsResponse, TikTokPostItem } from './tiktok-api-types';

export class TikTokApiService {
  private config: TikTokApiConfig;

  constructor(config: TikTokApiConfig) {
    this.config = config;
  }

  /**
   * Fetch winning products from TikTok Shop API
   */
  async getWinningProducts(query: TikTokApiQuery): Promise<TikTokProduct[]> {
    const url = new URL(this.config.baseUrl);
    
    // Add query parameters
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined) {
        url.searchParams.append(key, value.toString());
      }
    });

    try {
      const response = await (global as any).fetch(url.toString(), {
        method: 'GET',
        headers: {
          'x-rapidapi-host': this.config.rapidApiHost,
          'x-rapidapi-key': this.config.rapidApiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`TikTok API request failed: ${response.status} ${response.statusText}`);
      }

      const data: TikTokApiResponse = await response.json() as TikTokApiResponse;

      if (data.code !== 0) {
        throw new Error(`TikTok API error: ${data.msg} (${data.error_code})`);
      }

      return data.data;
    } catch (error) {
      console.error('Error fetching TikTok products:', error);
      throw error;
    }
  }

  /**
   * Get top 100 winning products by fetching 5 batches of 20 products each
   */
  async getTop20WinningProducts(): Promise<TikTokProduct[]> {
    const allProducts: TikTokProduct[] = [];
    const batchSize = 20;
    const totalBatches = 5;

    console.log(`📦 Fetching ${totalBatches} batches of ${batchSize} products each...`);

    // Fetch 5 batches of 20 products each
    for (let page = 1; page <= totalBatches; page++) {
      try {
        console.log(`📡 Fetching batch ${page}/${totalBatches}...`);
        
        const batchProducts = await this.getWinningProducts({
          country_code: 'US',
          end_product_rating: 5,
          start_product_rating: 0,
          limit: batchSize,
          page: page,
        });

        console.log(`✅ Batch ${page} fetched: ${batchProducts.length} products`);
        allProducts.push(...batchProducts);

        // Add a small delay between requests to avoid rate limiting
        if (page < totalBatches) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        console.error(`❌ Error fetching batch ${page}:`, error);
        // Continue with other batches even if one fails
      }
    }

    console.log(`🎉 Total products fetched: ${allProducts.length}`);
    return allProducts;
  }

  /**
   * Parse commission rate from string format (e.g., "10.00%" -> 10.0)
   */
  static parseCommissionRate(commissionRateStr?: string): number {
    if (!commissionRateStr) return 0;
    return parseFloat(commissionRateStr.replace('%', ''));
  }

  /**
   * Parse price from string format (e.g., "$8.99" -> 8.99)
   */
  static parsePrice(priceStr: string): number {
    return parseFloat(priceStr.replace(/[$,]/g, ''));
  }

  /**
   * Parse sales amount from string format (e.g., "$7.91M" -> 7910000)
   */
  static parseSalesAmount(salesStr: string): number {
    const cleanStr = salesStr.replace(/[$,]/g, '');
    
    if (cleanStr.includes('K')) {
      return parseFloat(cleanStr.replace('K', '')) * 1000;
    } else if (cleanStr.includes('M')) {
      return parseFloat(cleanStr.replace('M', '')) * 1000000;
    } else if (cleanStr.includes('B')) {
      return parseFloat(cleanStr.replace('B', '')) * 1000000000;
    }
    
    return parseFloat(cleanStr);
  }

  /**
   * Calculate estimated GMV from total sales
   */
  static calculateEstimatedGMV(totalSalesStr: string): number {
    return this.parseSalesAmount(totalSalesStr);
  }

  /**
   * Estimate creators carrying based on related videos and authors
   */
  static estimateCreatorsCarrying(relatedVideos?: number, relatedAuthor?: number): number {
    // If we have related_author, use that as it's more accurate
    if (relatedAuthor) return relatedAuthor;
    
    // Otherwise estimate based on related videos (assume 1.5 videos per creator on average)
    if (relatedVideos) return Math.round(relatedVideos / 1.5);
    
    // Default fallback
    return 1;
  }

  /**
   * Calculate trend score based on multiple factors
   * Scores are boosted to range from 70-100 (SPARK-96)
   */
  static calculateTrendScore(
    soldCount: number,
    weekSoldCount: number,
    totalSalesUsd: string,
    productRating: number,
    relatedVideos?: number
  ): number {
    // Parse sales amount
    const salesAmount = this.parseSalesAmount(totalSalesUsd);
    
    // Normalize factors (0-100 scale)
    // Sales volume (40% weight) - normalize to 0-40
    const salesScore = Math.min((soldCount / 100000) * 40, 40);
    
    // Weekly momentum (25% weight) - normalize to 0-25
    const weeklyMomentum = Math.min((weekSoldCount / 50000) * 25, 25);
    
    // Revenue impact (20% weight) - normalize to 0-20
    const revenueScore = Math.min((salesAmount / 10000000) * 20, 20);
    
    // Product rating (10% weight) - normalize to 0-10
    const ratingScore = (productRating / 5) * 10;
    
    // Social proof/virality (5% weight) - normalize to 0-5
    const viralityScore = relatedVideos ? Math.min((relatedVideos / 1000) * 5, 5) : 0;
    
    const rawTrendScore = salesScore + weeklyMomentum + revenueScore + ratingScore + viralityScore;
    
    // Ensure raw score is between 0-100
    const normalizedScore = Math.min(Math.max(rawTrendScore, 0), 100);
    
    // Boost scores to range from 70-100 (SPARK-96)
    // Formula: boostedScore = 70 + (normalizedScore * 0.3)
    const boostedScore = 70 + (normalizedScore * 0.3);
    
    return Math.round(boostedScore);
  }

  /**
   * Fetch TikTok user info by username
   */
  async getUserInfo(uniqueId: string): Promise<TikTokUserInfoResponse> {
    const url = `https://tiktok-api23.p.rapidapi.com/api/user/info?uniqueId=${uniqueId}`;

    try {
      const response = await (global as any).fetch(url, {
        method: 'GET',
        headers: {
          'x-rapidapi-host': 'tiktok-api23.p.rapidapi.com',
          'x-rapidapi-key': this.config.rapidApiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`TikTok User API request failed: ${response.status} ${response.statusText}`);
      }

      const data: TikTokUserInfoResponse = await response.json() as TikTokUserInfoResponse;

      if (data.statusCode !== 0) {
        throw new Error(`TikTok User API error: ${data.status_msg}`);
      }

      return data;
    } catch (error) {
      console.error('Error fetching TikTok user info:', error);
      throw error;
    }
  }

  /**
   * Calculate engagement rate from user stats
   */
  static calculateEngagementRate(
    followerCount: number,
    averageViews: number,
    averageLikes?: number
  ): number {
    if (followerCount === 0) return 0;
    
    // Basic engagement rate: (average views / followers) * 100
    let engagementRate = (averageViews / followerCount) * 100;
    
    // If we have likes data, factor that in
    if (averageLikes) {
      const likeRate = (averageLikes / averageViews) * 100;
      engagementRate = (engagementRate + likeRate) / 2;
    }
    
    // Cap at 100%
    return Math.min(engagementRate, 100);
  }

  /**
   * Fetch TikTok user posts by secUid
   */
  async getUserPosts(secUid: string, count: number = 35, cursor: string = '0'): Promise<TikTokPostsResponse> {
    const url = `https://tiktok-api23.p.rapidapi.com/api/user/posts?secUid=${secUid}&count=${count}&cursor=${cursor}`;

    try {
      const response = await (global as any).fetch(url, {
        method: 'GET',
        headers: {
          'x-rapidapi-host': 'tiktok-api23.p.rapidapi.com',
          'x-rapidapi-key': this.config.rapidApiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`TikTok Posts API request failed: ${response.status} ${response.statusText}`);
      }

      const data: TikTokPostsResponse = await response.json() as TikTokPostsResponse;

      return data;
    } catch (error) {
      console.error('Error fetching TikTok user posts:', error);
      throw error;
    }
  }

  /**
   * Calculate engagement rate for a specific post
   */
  static calculatePostEngagementRate(
    viewCount: number,
    likeCount: number,
    shareCount: number,
    commentCount: number
  ): number {
    if (viewCount === 0) return 0;
    
    const totalEngagement = likeCount + shareCount + commentCount;
    return (totalEngagement / viewCount) * 100;
  }
}

/**
 * Create TikTok API service instance with environment configuration
 */
export function createTikTokApiService(): TikTokApiService {
  const config: TikTokApiConfig = {
    rapidApiKey: process.env.RAPIDAPI_KEY || '**************************************************',
    rapidApiHost: 'tiktok-shop-analysis.p.rapidapi.com',
    baseUrl: 'https://tiktok-shop-analysis.p.rapidapi.com/analysis',
  };

  return new TikTokApiService(config);
}
