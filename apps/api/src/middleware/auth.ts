import { Request, Response, NextFunction } from 'express';
import { AppError } from './error-handler';
import { db } from '@xact-data/database';

export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    whopId?: string;
    name?: string;
    image?: string;
  };
}

/**
 * Authentication middleware that extracts user information from headers
 * This middleware expects the client to send user information in headers
 * In a production environment, you would validate JWT tokens or session cookies
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // For now, we'll extract user info from headers
    // In production, you would validate JWT tokens or session cookies
    const whopId = req.headers['x-whop-id'] as string;
    const userEmail = req.headers['x-user-email'] as string;
    const userName = req.headers['x-user-name'] as string;
    const userImage = req.headers['x-user-image'] as string;

    if (!whopId && !userEmail) {
      throw new AppError('Authentication required', 401);
    }

    // Try to find user by whopId first, then by email
    let user;
    if (whopId) {
      user = await db.user.findUnique({
        where: { whopId },
        select: {
          id: true,
          email: true,
          whopId: true,
          name: true,
          image: true,
        },
      });
    }

    if (!user && userEmail) {
      user = await db.user.findUnique({
        where: { email: userEmail },
        select: {
          id: true,
          email: true,
          whopId: true,
          name: true,
          image: true,
        },
      });
    }

    if (!user) {
      throw new AppError('User not found', 404);
    }

    // Attach user to request
    req.user = {
      id: user.id,
      email: user.email,
      whopId: user.whopId || undefined,
      name: user.name || undefined,
      image: user.image || undefined,
    };

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication middleware - doesn't throw if user is not authenticated
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const whopId = req.headers['x-whop-id'] as string;
    const userEmail = req.headers['x-user-email'] as string;

    if (!whopId && !userEmail) {
      return next();
    }

    // Try to find user by whopId first, then by email
    let user;
    if (whopId) {
      user = await db.user.findUnique({
        where: { whopId },
        select: {
          id: true,
          email: true,
          whopId: true,
          name: true,
          image: true,
        },
      });
    }

    if (!user && userEmail) {
      user = await db.user.findUnique({
        where: { email: userEmail },
        select: {
          id: true,
          email: true,
          whopId: true,
          name: true,
          image: true,
        },
      });
    }

    if (user) {
      req.user = {
        id: user.id,
        email: user.email,
        whopId: user.whopId || undefined,
        name: user.name || undefined,
        image: user.image || undefined,
      };
    }

    next();
  } catch (error) {
    // Don't fail on optional auth errors
    next();
  }
};
