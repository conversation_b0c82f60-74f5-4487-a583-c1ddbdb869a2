import { AIClient, AIConfig } from '@xact-data/ai-wrapper';
import { prisma } from '@xact-data/database';
import { Creator, CompetitorTracking, CompetitorAnalysisType, createTikTokApiService, transformTikTokPostsToCreatorVideos, extractPostInsights } from '@xact-data/shared';

export class CompetitorAnalysisService {
  private aiClient: AIClient;

  constructor() {
    const aiConfig: AIConfig = {
      provider: 'gemini',
      apiKey: process.env.GEMINI_API_KEY || 'AIzaSyAij22a341Hlw_DMlF5MRI-caOIni065xo',
      model: process.env.AI_MODEL || 'gemini-2.0-flash-exp',
      maxTokens: 2000,
      temperature: 0.7,
    };

    this.aiClient = new AIClient(aiConfig);
  }

  /**
   * Fetch and store posts data for a competitor
   */
  async fetchAndStorePostsData(competitorTrackingId: string): Promise<void> {
    const competitorTracking = await prisma.competitorTracking.findUnique({
      where: { id: competitorTrackingId },
      include: { creator: true },
    });

    if (!competitorTracking || !competitorTracking.creator.secUid) {
      throw new Error('Competitor tracking not found or missing secUid');
    }

    const tikTokService = createTikTokApiService();
    
    try {
      // Fetch posts from TikTok API
      const postsResponse = await tikTokService.getUserPosts(competitorTracking.creator.secUid, 35);
      
      if (postsResponse.data?.itemList) {
        // Transform posts to database format
        const videoData = transformTikTokPostsToCreatorVideos(
          postsResponse.data.itemList,
          competitorTracking.creator.id
        );

        // Store videos in database (upsert to avoid duplicates)
        for (const video of videoData) {
          await prisma.creatorVideo.upsert({
            where: { id: video.id },
            update: {
              viewCount: video.viewCount,
              likeCount: video.likeCount,
              shareCount: video.shareCount,
              commentCount: video.commentCount,
              engagementRate: video.engagementRate,
              updatedAt: new Date(),
            },
            create: video,
          });
        }

        console.log(`✅ Stored ${videoData.length} posts for ${competitorTracking.creator.username}`);
      }
    } catch (error) {
      console.error(`Failed to fetch posts for ${competitorTracking.creator.username}:`, error);
      // Don't throw error - analysis can continue with existing video data
    }
  }

  /**
   * Generate strengths and weaknesses analysis for a competitor
   */
  async generateStrengthsWeaknessesAnalysis(
    competitorTrackingId: string,
    userCreatorData?: any
  ): Promise<any> {
    // First, fetch and store latest posts data
    await this.fetchAndStorePostsData(competitorTrackingId);

    const competitorTracking = await prisma.competitorTracking.findUnique({
      where: { id: competitorTrackingId },
      include: {
        creator: {
          include: {
            videos: { take: 20, orderBy: { publishedAt: 'desc' } },
            creatorProducts: { 
              include: { product: true },
              take: 5,
              orderBy: { gmv: 'desc' }
            },
          },
        },
      },
    });

    if (!competitorTracking) {
      throw new Error('Competitor tracking not found');
    }

    const competitorData = this.formatCreatorDataForAI(competitorTracking.creator);
    const userData = userCreatorData ? this.formatCreatorDataForAI(userCreatorData) : 'No user data provided';

    const prompt = `I need you to analyze this competitor so I can beat them. Here's their profile:

${competitorData}

${userCreatorData ? `Here's my current profile for comparison:
${userData}` : ''}

Write me a comprehensive battle plan to dominate this competitor. Structure your response as a strategic report with clear sections:

**🎯 THEIR WEAKNESSES (Vulnerabilities I Can Exploit)**
Identify 3-5 specific weaknesses in their strategy, content, or approach that I can exploit to steal their audience. Be specific about what they're doing wrong and how I can capitalize on it.

**💪 MY COMPETITIVE ADVANTAGES** 
Based on the data, explain 3-5 ways I can outperform them. Focus on gaps in their strategy that I can fill better.

**🚀 UNTAPPED MARKET OPPORTUNITIES**
Show me 3-5 specific areas they're completely missing that I can dominate. Think about content angles, product categories, audience segments, or engagement tactics they're ignoring.

**⚔️ MY ATTACK STRATEGY**
Give me specific, actionable tactics to outrank them and steal market share. Include content ideas, posting strategies, product recommendations, and engagement tactics.

**🔥 THREAT ASSESSMENT**
Rate them 0-100 on how dangerous they are as competition and explain why. Tell me how urgently I need to act and what timeline I'm working with.

Write this like you're my personal strategist planning a takeover. Be conversational, motivational, and focused on MY victory. Use specific examples and actionable advice, not generic suggestions.`;

    const analysisContent = await this.aiClient.generateInsight(prompt);

    const analysis = await prisma.competitorAnalysis.create({
      data: {
        competitorTrackingId,
        analysisType: 'STRENGTHS_WEAKNESSES',
        title: `Battle Plan vs ${competitorTracking.creator.displayName || competitorTracking.creator.username}`,
        content: analysisContent,
      },
    });

    return analysis;
  }

  /**
   * Generate content analysis for a competitor
   */
  async generateContentAnalysis(competitorTrackingId: string): Promise<any> {
    // First, fetch and store latest posts data
    await this.fetchAndStorePostsData(competitorTrackingId);

    const competitorTracking = await prisma.competitorTracking.findUnique({
      where: { id: competitorTrackingId },
      include: {
        creator: {
          include: {
            videos: { take: 30, orderBy: { publishedAt: 'desc' } },
          },
        },
      },
    });

    if (!competitorTracking) {
      throw new Error('Competitor tracking not found');
    }

    const videoData = competitorTracking.creator.videos.map(video => ({
      title: video.title,
      views: video.viewCount,
      likes: video.likeCount,
      shares: video.shareCount,
      comments: video.commentCount,
      engagement: video.engagementRate,
      publishedAt: video.publishedAt,
      hashtags: video.hashtags,
    }));

    const prompt = `I want to study this competitor's content so I can create better content and steal their audience. Here's their data:

Competitor: ${competitorTracking.creator.displayName || competitorTracking.creator.username}
Followers: ${competitorTracking.creator.followerCount.toLocaleString()}
Engagement: ${competitorTracking.creator.engagementRate}%

Their Recent Videos:
${JSON.stringify(videoData, null, 2)}

Analyze their content strategy and tell me:

1. **What They're Doing Right** (Content themes that work for them)
2. **Their Weak Spots** (Content gaps I can exploit)
3. **Their Best Hooks** (So I can create better ones)
4. **Their CTA Strategy** (So I can outperform it)
5. **Their Hashtag Game** (Trends I should hijack)
6. **My Opportunity** (Content angles they're missing that I can dominate)
7. **My Action Plan** (Specific content I should create to outrank them)

Write this like you're helping me build a content strategy to crush this competitor. Be specific about what I should do differently to win their audience.`;

    const analysisContent = await this.aiClient.generateInsight(prompt);

    const analysis = await prisma.competitorAnalysis.create({
      data: {
        competitorTrackingId,
        analysisType: 'CONTENT_ANALYSIS',
        title: `Content Strategy to Beat ${competitorTracking.creator.displayName || competitorTracking.creator.username}`,
        content: analysisContent,
      },
    });

    return analysis;
  }

  /**
   * Generate competitive gap analysis between user and competitor
   */
  async generateGapAnalysis(
    competitorTrackingId: string,
    userCreatorData: any
  ): Promise<any> {
    // First, fetch and store latest posts data
    await this.fetchAndStorePostsData(competitorTrackingId);

    const competitorTracking = await prisma.competitorTracking.findUnique({
      where: { id: competitorTrackingId },
      include: {
        creator: {
          include: {
            videos: { take: 20, orderBy: { publishedAt: 'desc' } },
            creatorProducts: { 
              include: { product: true },
              take: 5,
              orderBy: { gmv: 'desc' }
            },
          },
        },
      },
    });

    if (!competitorTracking) {
      throw new Error('Competitor tracking not found');
    }

    const competitorData = this.formatCreatorDataForAI(competitorTracking.creator);
    const userData = this.formatCreatorDataForAI(userCreatorData);

    const prompt = `I need a battle plan to dominate this competitor. Here's the situation:

MY CURRENT STATS:
${userData}

COMPETITOR I WANT TO CRUSH:
${competitorData}

Show me exactly how to close the gap and surpass them:

1. **Where I'm Losing** (Performance gaps I need to close urgently)
2. **Content I'm Missing** (What content types will steal their audience)
3. **Audiences to Target** (Their audience segments I should go after)
4. **Products to Promote** (High-converting products they're using that I should grab)
5. **Engagement Tactics** (Their engagement strategies I should copy and improve)
6. **My Winning Moves** (Specific opportunities to leapfrog them)
7. **90-Day Domination Plan** (Step-by-step roadmap to beat them)
8. **Victory Timeline** (When I'll start seeing results)

Write this like you're my personal strategist planning a takeover. Be aggressive, specific, and focused on my victory.`;

    const analysisContent = await this.aiClient.generateCompetitorAnalysis(userData, competitorData);

    const analysis = await prisma.competitorAnalysis.create({
      data: {
        competitorTrackingId,
        analysisType: 'GAP_OPPORTUNITIES',
        title: `Domination Plan vs ${competitorTracking.creator.displayName || competitorTracking.creator.username}`,
        content: analysisContent,
      },
    });

    return analysis;
  }

  /**
   * Generate growth strategy based on competitor analysis
   */
  async generateGrowthStrategy(competitorTrackingId: string): Promise<any> {
    // First, fetch and store latest posts data
    await this.fetchAndStorePostsData(competitorTrackingId);

    const competitorTracking = await prisma.competitorTracking.findUnique({
      where: { id: competitorTrackingId },
      include: {
        creator: {
          include: {
            videos: { take: 25, orderBy: { publishedAt: 'desc' } },
            creatorProducts: { 
              include: { product: true },
              take: 10,
              orderBy: { gmv: 'desc' }
            },
          },
        },
        analyses: {
          take: 3,
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!competitorTracking) {
      throw new Error('Competitor tracking not found');
    }

    const creatorData = this.formatCreatorDataForAI(competitorTracking.creator);
    const previousAnalyses = competitorTracking.analyses
      .map(a => `${a.title}: ${a.content.substring(0, 500)}...`)
      .join('\n\n');

    const prompt = `This competitor is successful and I want to model their winning strategies while surpassing them. Here's what I know:

SUCCESSFUL COMPETITOR:
${creatorData}

WHAT I'VE LEARNED SO FAR:
${previousAnalyses}

Build me a growth strategy to not just copy them, but dominate their space:

1. **30-Day Blitz** (Quick moves to gain momentum and close gaps)
2. **90-Day Takeover** (Strategies to match and exceed their performance)
3. **6-Month Domination** (Long-term plan to become the market leader)
4. **Content Warfare** (Content strategy to outperform and outrank them)
5. **Product Domination** (Product strategy to steal their market share)
6. **Audience Capture** (How to build a more engaged audience than theirs)
7. **Victory Metrics** (KPIs that show I'm winning against them)
8. **Threat Management** (How to handle their responses to my success)
9. **Investment Strategy** (Where to spend money for maximum competitive advantage)
10. **Milestone Victories** (Clear targets that show I'm beating them)

Write this like you're my business coach creating a plan for total market domination. Be specific, ambitious, and focused on crushing the competition.`;

    const analysisContent = await this.aiClient.generateInsight(prompt);

    const analysis = await prisma.competitorAnalysis.create({
      data: {
        competitorTrackingId,
        analysisType: 'GROWTH_STRATEGY',
        title: `Market Domination Strategy Inspired by ${competitorTracking.creator.displayName || competitorTracking.creator.username}`,
        content: analysisContent,
      },
    });

    return analysis;
  }

  /**
   * Format creator data for AI analysis
   */
  private formatCreatorDataForAI(creator: any): string {
    const recentVideos = creator.videos?.slice(0, 10).map((video: any) => ({
      id: video.id,
      title: video.title,
      description: video.description,
      views: video.viewCount,
      likes: video.likeCount,
      shares: video.shareCount,
      comments: video.commentCount,
      engagement: video.engagementRate,
      publishedAt: video.publishedAt,
      hashtags: video.hashtags,
    })) || [];

    const topProducts = creator.creatorProducts?.slice(0, 3).map((cp: any) => ({
      title: cp.product.title,
      category: cp.product.category,
      gmv: cp.gmv,
      sales: cp.sales,
      price: cp.product.price,
      commissionRate: cp.product.commissionRate,
    })) || [];

    // Extract content insights from videos for enhanced analysis
    let contentInsights = '';
    if (creator.videos && creator.videos.length > 0) {
      const allHashtags: Record<string, number> = {};
      const totalViews = creator.videos.reduce((sum: number, v: any) => sum + v.viewCount, 0);
      const totalLikes = creator.videos.reduce((sum: number, v: any) => sum + v.likeCount, 0);
      const totalShares = creator.videos.reduce((sum: number, v: any) => sum + v.shareCount, 0);
      const totalComments = creator.videos.reduce((sum: number, v: any) => sum + v.commentCount, 0);
      
      // Extract hashtag patterns
      creator.videos.forEach((video: any) => {
        if (video.hashtags && Array.isArray(video.hashtags)) {
          video.hashtags.forEach((hashtag: string) => {
            allHashtags[hashtag] = (allHashtags[hashtag] || 0) + 1;
          });
        }
      });

      const topHashtags = Object.entries(allHashtags)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([hashtag, count]) => `${hashtag} (${count} times)`);

      const avgViews = Math.round(totalViews / creator.videos.length);
      const avgLikes = Math.round(totalLikes / creator.videos.length);
      const avgShares = Math.round(totalShares / creator.videos.length);
      const avgComments = Math.round(totalComments / creator.videos.length);

      contentInsights = `

CONTENT PERFORMANCE INSIGHTS:
Average Views per Video: ${avgViews.toLocaleString()}
Average Likes per Video: ${avgLikes.toLocaleString()}
Average Shares per Video: ${avgShares.toLocaleString()}
Average Comments per Video: ${avgComments.toLocaleString()}
Content Engagement Rate: ${((avgLikes + avgShares + avgComments) / avgViews * 100).toFixed(2)}%

TOP HASHTAGS USED:
${topHashtags.join('\n')}

BEST PERFORMING VIDEOS (Top 3):
${creator.videos
  .sort((a: any, b: any) => b.viewCount - a.viewCount)
  .slice(0, 3)
  .map((v: any, i: number) => `${i + 1}. "${v.title || v.description?.substring(0, 50) || 'No title'}" - ${v.viewCount.toLocaleString()} views, ${v.likeCount.toLocaleString()} likes`)
  .join('\n')}`;
    }

    return `
Username: ${creator.username}
Display Name: ${creator.displayName || 'N/A'}
Bio: ${creator.bio || 'N/A'}
Followers: ${creator.followerCount?.toLocaleString() || '0'}
Following: ${creator.followingCount?.toLocaleString() || '0'}
Total Likes: ${creator.likesCount?.toLocaleString() || '0'}
Video Count: ${creator.videoCount?.toLocaleString() || '0'}
Average Views: ${creator.averageViews?.toLocaleString() || '0'}
Engagement Rate: ${creator.engagementRate || 0}%
Total GMV: $${creator.totalGMV?.toLocaleString() || '0'}
Verified: ${creator.isVerified ? 'Yes' : 'No'}
${contentInsights}

RECENT VIDEOS DETAILED ANALYSIS (${recentVideos.length}):
${JSON.stringify(recentVideos, null, 2)}

TOP PRODUCTS (${topProducts.length}):
${JSON.stringify(topProducts, null, 2)}
    `.trim();
  }
}

export const competitorAnalysisService = new CompetitorAnalysisService();