import { Router } from 'express';
import {
  bookmarkCreator,
  unbookmarkCreator,
  getBookmarkedCreators,
  checkBookmarkStatus,
} from '../controllers/bookmarks';
import { authenticate } from '../middleware/auth';

const router = Router();

// Apply authentication middleware to all bookmark routes
router.use(authenticate);

router.post('/', bookmarkCreator);
router.get('/', getBookmarkedCreators);
router.get('/check/:creatorId', checkBookmarkStatus);
router.delete('/:creatorId', unbookmarkCreator);

export default router;
