import { Router } from 'express';
import {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getTrendingProducts,
  getProductAIAnalysis,
} from '../controllers/products';

const router = Router();

router.get('/', getProducts);
router.get('/trending', getTrendingProducts);
router.get('/:id/ai-analysis', getProductAIAnalysis);
router.get('/:id', getProduct);
router.post('/', createProduct);
router.put('/:id', updateProduct);
router.delete('/:id', deleteProduct);

export default router;