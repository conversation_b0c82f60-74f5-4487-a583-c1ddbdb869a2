import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '@xact-data/database';
import { createApiResponse, ProductSchema } from '@xact-data/shared';
import { asyncHandler, AppError } from '../middleware/error-handler';
import { AIClient } from '@xact-data/ai-wrapper';

// Helper function to convert BigInt to string for JSON serialization
function serializeProduct(product: any) {
  return {
    ...product,
    lastTimeStamp: product.lastTimeStamp ? product.lastTimeStamp.toString() : null,
  };
}

const GetProductsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  category: z.string().optional(),
  minTrendScore: z.coerce.number().min(0).max(100).optional(),
  minGMV: z.coerce.number().min(0).optional(),
  maxGMV: z.coerce.number().min(0).optional(),
  sortBy: z.enum(['trendScore', 'soldIn24h', 'estimatedGMV', 'commissionRate']).default('trendScore'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const getProducts = asyncHandler(async (req: Request, res: Response) => {
  const query = GetProductsQuerySchema.parse(req.query);
  
  // Build GMV filter conditions
  const gmvFilter: any = {};
  if (query.minGMV && query.minGMV > 0) {
    gmvFilter.gte = query.minGMV;
  }
  if (query.maxGMV && query.maxGMV > 0) {
    gmvFilter.lte = query.maxGMV;
  }
  
  const where = {
    ...(query.category && { category: query.category }),
    ...(query.minTrendScore && { trendScore: { gte: query.minTrendScore } }),
    ...(Object.keys(gmvFilter).length > 0 && { estimatedGMV: gmvFilter }),
  };

  const orderBy = {
    [query.sortBy]: query.sortOrder,
  };

  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      orderBy,
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.product.count({ where }),
  ]);

  const totalPages = Math.ceil(total / query.limit);

  // Serialize products to handle BigInt values
  const serializedProducts = products.map(serializeProduct);

  res.json(
    createApiResponse(true, {
      products: serializedProducts,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages,
        hasNext: query.page < totalPages,
        hasPrev: query.page > 1,
      },
    })
  );
});

export const getProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      creatorProducts: {
        include: {
          creator: true,
        },
        orderBy: {
          gmv: 'desc',
        },
        take: 10,
      },
    },
  });

  if (!product) {
    throw new AppError('Product not found', 404);
  }

  res.json(createApiResponse(true, serializeProduct(product)));
});

const CreateProductSchema = ProductSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const createProduct = asyncHandler(async (req: Request, res: Response) => {
  const data = CreateProductSchema.parse(req.body);

  const product = await prisma.product.create({
    data: {
      id: `prod_${Date.now()}`, // Simple ID generation
      title: data.title,
      category: data.category,
      commissionRate: data.commissionRate,
      soldIn24h: data.soldIn24h,
      creatorsCarrying: data.creatorsCarrying,
      estimatedGMV: data.estimatedGMV,
      trendScore: data.trendScore,
      affiliateLink: data.affiliateLink,
      imageUrl: data.imageUrl,
      price: data.price,
    },
  });

  res.status(201).json(createApiResponse(true, serializeProduct(product), 'Product created successfully'));
});

export const updateProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const data = CreateProductSchema.partial().parse(req.body);

  const product = await prisma.product.update({
    where: { id },
    data,
  });

  res.json(createApiResponse(true, serializeProduct(product), 'Product updated successfully'));
});

export const deleteProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  await prisma.product.delete({
    where: { id },
  });

  res.json(createApiResponse(true, null, 'Product deleted successfully'));
});

export const getTrendingProducts = asyncHandler(async (req: Request, res: Response) => {
  const products = await prisma.product.findMany({
    where: {
      trendScore: { gte: 70 },
    },
    orderBy: {
      trendScore: 'desc',
    },
    take: 10,
  });

  // Serialize products to handle BigInt values
  const serializedProducts = products.map(serializeProduct);

  res.json(createApiResponse(true, serializedProducts));
});

export const getProductAIAnalysis = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const product = await prisma.product.findUnique({
    where: { id },
  });

  if (!product) {
    throw new AppError('Product not found', 404);
  }

  // Initialize AI client with Gemini
  const aiClient = new AIClient({
    provider: 'gemini',
    apiKey: process.env.GEMINI_API_KEY || 'AIzaSyAij22a341Hlw_DMlF5MRI-caOIni065xo',
    model: 'gemini-2.0-flash-exp',
    maxTokens: 3000,
    temperature: 0.7,
  });

  // Generate AI analysis using Gemini
  const prompt = `You are a TikTok Shop expert analyzing a product to help creators maximize their sales and commissions. Generate a comprehensive analysis for the following product:

Product Details:
- Title: ${product.title}
- Category: ${product.category}
- Price: $${product.price}
- Commission Rate: ${product.commissionRate}%
- 24h Sales: ${product.soldIn24h}
- Creators Promoting: ${product.creatorsCarrying}
- Estimated GMV: $${product.estimatedGMV}
- Trend Score: ${product.trendScore}
- Free Shipping: ${product.freeShipping ? 'Yes' : 'No'}
${product.productRating ? `- Rating: ${product.productRating}/5` : ''}

Please provide a structured JSON response with the following format:

{
  "objections": [
    {
      "objection": "Common customer objection",
      "handling": "How to handle this objection effectively",
      "priority": "high|medium|low"
    }
  ],
  "videoFormats": {
    "shortForm": [
      {
        "format": "Format name (duration)",
        "description": "What makes this format effective",
        "bestFor": "Ideal audience or use case"
      }
    ],
    "longForm": [
      {
        "format": "Format name (duration)",
        "description": "What makes this format effective",
        "bestFor": "Ideal audience or use case"
      }
    ]
  },
  "livestreamFormats": [
    {
      "format": "Stream format name",
      "description": "How to execute this format effectively",
      "timing": "Best time and duration"
    }
  ],
  "messaging": {
    "hooks": [
      "Attention-grabbing hook line 1",
      "Hook line 2"
    ],
    "callToActions": [
      "Effective CTA 1",
      "CTA 2"
    ],
    "keyPoints": [
      "Key talking point 1",
      "Talking point 2"
    ]
  }
}

Provide at least:
- 5 common objections with handling strategies
- 4 short-form video formats
- 3 long-form video formats
- 4 livestream formats
- 5 hooks, 5 CTAs, and 6 key talking points

Focus on specific, actionable advice that creators can use immediately. Consider the product's price point, category, and performance metrics when generating recommendations.`;

  const analysis = await aiClient.generateInsight(prompt);

  // Parse the AI response
  try {
    // Extract JSON from the response (handle markdown code blocks if present)
    let jsonStr = analysis;
    const jsonMatch = analysis.match(/```(?:json)?\s*([\s\S]*?)```/);
    if (jsonMatch) {
      jsonStr = jsonMatch[1];
    }
    
    const aiAnalysis = JSON.parse(jsonStr);
    
    res.json(createApiResponse(true, {
      product: serializeProduct(product),
      aiAnalysis,
    }));
  } catch (error) {
    console.error('Error parsing AI response:', error);
    console.error('Raw AI response:', analysis);
    throw new AppError('Failed to generate AI analysis. Please try again.', 500);
  }
});