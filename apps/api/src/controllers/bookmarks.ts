import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '@xact-data/database';
import { createApiResponse } from '@xact-data/shared';
import { asyncHandler, AppError } from '../middleware/error-handler';

const BookmarkCreatorSchema = z.object({
  creatorId: z.string(),
});

const GetBookmarksQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
});

export const bookmarkCreator = asyncHandler(async (req: Request, res: Response) => {
  const { creatorId } = BookmarkCreatorSchema.parse(req.body);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Check if creator exists
  const creator = await prisma.creator.findUnique({
    where: { id: creatorId },
  });

  if (!creator) {
    throw new AppError('Creator not found', 404);
  }

  // Check if already bookmarked
  const existingBookmark = await prisma.bookmarkedCreator.findUnique({
    where: {
      userId_creatorId: {
        userId,
        creatorId,
      },
    },
  });

  if (existingBookmark) {
    throw new AppError('Creator already bookmarked', 409);
  }

  // Create bookmark
  const bookmark = await prisma.bookmarkedCreator.create({
    data: {
      userId,
      creatorId,
    },
    include: {
      creator: true,
    },
  });

  res.status(201).json(
    createApiResponse(true, bookmark, 'Creator bookmarked successfully')
  );
});

export const unbookmarkCreator = asyncHandler(async (req: Request, res: Response) => {
  const { creatorId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  // Find and delete bookmark
  const bookmark = await prisma.bookmarkedCreator.findUnique({
    where: {
      userId_creatorId: {
        userId,
        creatorId,
      },
    },
  });

  if (!bookmark) {
    throw new AppError('Bookmark not found', 404);
  }

  await prisma.bookmarkedCreator.delete({
    where: {
      id: bookmark.id,
    },
  });

  res.json(
    createApiResponse(true, null, 'Creator unbookmarked successfully')
  );
});

export const getBookmarkedCreators = asyncHandler(async (req: Request, res: Response) => {
  const query = GetBookmarksQuerySchema.parse(req.query);
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const [bookmarks, total] = await Promise.all([
    prisma.bookmarkedCreator.findMany({
      where: { userId },
      include: {
        creator: {
          include: {
            creatorProducts: {
              include: {
                product: true,
              },
              orderBy: {
                gmv: 'desc',
              },
              take: 3, // Top 3 products
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.bookmarkedCreator.count({
      where: { userId },
    }),
  ]);

  const totalPages = Math.ceil(total / query.limit);

  res.json(
    createApiResponse(true, {
      bookmarks,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages,
        hasNext: query.page < totalPages,
        hasPrev: query.page > 1,
      },
    })
  );
});

export const checkBookmarkStatus = asyncHandler(async (req: Request, res: Response) => {
  const { creatorId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new AppError('User not authenticated', 401);
  }

  const bookmark = await prisma.bookmarkedCreator.findUnique({
    where: {
      userId_creatorId: {
        userId,
        creatorId,
      },
    },
  });

  res.json(
    createApiResponse(true, { isBookmarked: !!bookmark })
  );
});
