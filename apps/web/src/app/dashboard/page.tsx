'use client';

import { AffiliateDashboard } from '../../components/AffiliateDashboard';
import { AppLayout } from '../../components/layout/AppLayout';
import Button from '../../components/ui/Button';
import { TargetIcon } from 'lucide-react';
import { MagicWand01 as BrainIcon } from '@untitled-ui/icons-react';
import { TikTokConnectionButton } from '../../components/tiktok/TikTokConnectionButton';

// Subtle sparkle icon component
const SubtleSparklesIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" width="20" height="20">
    <defs>
      <linearGradient id="subtleSparkleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="rgb(59 130 246)" />
        <stop offset="50%" stopColor="rgb(37 99 235)" />
        <stop offset="100%" stopColor="rgb(147 51 234)" />
      </linearGradient>
    </defs>
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} stroke="url(#subtleSparkleGradient)" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z" />
  </svg>
);

export default function DashboardPage() {
  const generateAIInsight = async (type: string) => {
    console.log('Generating AI insight:', type);
  };

  return (
    <AppLayout>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Affiliate Dashboard</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Your growth headquarters with real-time performance tracking and AI-powered insights.
            </p>
          </div>

          {/* TikTok Connection Button */}
          <div className="flex items-center">
            <TikTokConnectionButton />
          </div>
        </div>

        <AffiliateDashboard />
      </div>
    </AppLayout>
  );
}