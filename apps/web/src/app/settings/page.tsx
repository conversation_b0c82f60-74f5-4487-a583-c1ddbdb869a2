'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { TikTokSettingsSection } from '../../components/settings/TikTokSettingsSection';
import {
  User01 as UserIcon,
  Shield01 as ShieldIcon,
  CreditCard01 as CreditCardIcon,
  Save01 as SaveIcon,
  Link01 as LinkIcon,
  BookOpen01 as BookIcon,
  PlayCircle as PlayIcon,
  HelpCircle as HelpIcon
} from '@untitled-ui/icons-react';
import { ExternalLinkIcon, MessageCircleIcon } from 'lucide-react';

export default function SettingsPage() {
  const { data: session } = useSession();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'profile');

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  const tabs = [
    { id: 'profile', label: 'Profile', icon: UserIcon },
    { id: 'integrations', label: 'Integrations', icon: LinkIcon },
    { id: 'security', label: 'Security', icon: ShieldIcon },
    { id: 'billing', label: 'Billing', icon: CreditCardIcon },
    { id: 'support', label: 'Support', icon: HelpIcon },
    { id: 'docs', label: 'Documentation', icon: BookIcon },
    { id: 'videos', label: 'Video Tutorials', icon: PlayIcon },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold text-black dark:text-white mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Manage your account settings and preferences
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <Card className="p-4 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]" data-rounded="default">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors font-sans ${
                      activeTab === tab.id
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                    style={{
                      backgroundColor: activeTab === tab.id ? 'var(--dark-border, #f3f4f6)' : 'transparent',
                      color: activeTab === tab.id ? 'var(--dark-text, #111827)' : 'var(--dark-text-secondary, #6b7280)',
                    }}
                    onMouseEnter={(e) => {
                      if (activeTab !== tab.id) {
                        e.currentTarget.style.backgroundColor = 'var(--dark-border, #f9fafb)';
                        e.currentTarget.style.color = 'var(--dark-text, #111827)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (activeTab !== tab.id) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.color = 'var(--dark-text-secondary, #6b7280)';
                      }
                    }}
                    data-rounded="default"
                  >
                    <tab.icon className="w-5 h-5" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === 'profile' && (
              <Card className="p-6 bg-white border border-gray-200" data-rounded="default" style={{ backgroundColor: 'var(--dark-content, white)', borderColor: 'var(--dark-border, #e5e7eb)' }}>
                <h2 className="text-xl font-semibold text-gray-900 mb-6 font-sans" style={{ color: 'var(--dark-text, #111827)' }}>Profile Information</h2>
                <div className="space-y-6">
                  <div className="flex items-center gap-6">
                    <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      <UserIcon className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                    </div>
                    <div>
                      <Button
                        intent="gray"
                        variant="outlined"
                        size="sm"
                        className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700 shadow-sm"
                        data-rounded="default"
                      >
                        Change Photo
                      </Button>
                      <p className="text-sm text-gray-500 mt-1 font-sans">JPG, GIF or PNG. Max size 2MB.</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                        Full Name
                      </label>
                      <input
                        type="text"
                        defaultValue={session?.user?.name || ""}
                        placeholder="Enter your full name"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                        style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                        data-rounded="default"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                        Email Address
                      </label>
                      <input
                        type="email"
                        defaultValue={session?.user?.email || ""}
                        placeholder="Enter your email address"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-sans"
                        data-rounded="default"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                        TikTok Username
                      </label>
                      <input
                        type="text"
                        defaultValue="@johndoe"
                        placeholder="@username"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-sans"
                        data-rounded="default"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        defaultValue="+****************"
                        placeholder="+****************"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-sans"
                        data-rounded="default"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                      Bio
                    </label>
                    <textarea
                      rows={4}
                      defaultValue="TikTok creator focused on tech and lifestyle content..."
                      placeholder="Tell us about yourself..."
                      className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-sans"
                      data-rounded="default"
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button
                      intent="gray"
                      variant="outlined"
                      className="flex items-center gap-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700 shadow-sm"
                      data-rounded="default"
                    >
                      <SaveIcon className="w-4 h-4" />
                      Save Changes
                    </Button>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'integrations' && (
              <div className="space-y-6">
                <Card className="p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700" data-rounded="default">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6 font-sans">Connected Accounts</h2>
                  <div className="space-y-6">
                    <TikTokSettingsSection />

                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg" data-rounded="default">
                      <div className="flex items-start gap-3">
                        <div className="w-5 h-5 text-blue-600 mt-0.5">
                          <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-blue-900 font-sans">Why connect your accounts?</h4>
                          <p className="text-sm text-blue-800 mt-1 font-sans">
                            Connecting your seller accounts allows Xact Data to automatically sync your sales data,
                            track performance metrics, and provide personalized insights to help grow your business.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}



            {activeTab === 'security' && (
              <Card className="p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6 font-sans">Security Settings</h2>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 font-sans">Password</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                          Current Password
                        </label>
                        <input
                          type="password"
                          placeholder="Enter current password"
                          className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-sans"
                          data-rounded="default"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                          New Password
                        </label>
                        <input
                          type="password"
                          placeholder="Enter new password"
                          className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-sans"
                          data-rounded="default"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          placeholder="Confirm new password"
                          className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 font-sans"
                          data-rounded="default"
                        />
                      </div>
                      <Button
                        intent="gray"
                        variant="outlined"
                        className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700 shadow-sm"
                        data-rounded="default"
                      >
                        Update Password
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 font-sans">Two-Factor Authentication</h3>
                    <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100 font-sans">Enable 2FA</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400 font-sans">Add an extra layer of security to your account</p>
                      </div>
                      <Button
                        intent="gray"
                        variant="outlined"
                        className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-600 shadow-sm"
                        data-rounded="default"
                      >
                        Enable
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'billing' && (
              <Card className="p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6 font-sans">Billing & Subscription</h2>
                <div className="space-y-6">
                  <p className="text-gray-600 dark:text-gray-400 font-sans">
                    Your subscription is managed through Whop. Click below to manage your subscription, update payment methods, or view billing history.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      intent="primary"
                      onClick={() => window.open('https://whop.com/hub/memberships/', '_blank')}
                      data-rounded="default"
                      className="flex items-center justify-center gap-2"
                    >
                      <CreditCardIcon className="w-4 h-4" />
                      Manage Subscription
                      <ExternalLinkIcon className="w-4 h-4" />
                    </Button>
                    <Button
                      intent="gray"
                      variant="outlined"
                      onClick={() => window.open('https://whop.com/hub/billing/', '_blank')}
                      data-rounded="default"
                      className="flex items-center justify-center gap-2 border border-gray-300 dark:border-gray-600"
                    >
                      Billing History
                      <ExternalLinkIcon className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'support' && (
              <Card className="p-6 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]" data-rounded="default">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 font-sans">Help & Support</h2>
                <div className="space-y-6">
                  <p className="text-gray-600 dark:text-gray-400 font-sans">
                    Need help? Our support team is available 24/7 through Whop's support portal.
                  </p>
                  <Button
                    intent="primary"
                    onClick={() => window.open('https://whop.com/hub/support/', '_blank')}
                    data-rounded="default"
                    className="flex items-center gap-2"
                  >
                    <MessageCircleIcon className="w-4 h-4" />
                    Open Support Chat
                    <ExternalLinkIcon className="w-4 h-4" />
                  </Button>
                </div>
              </Card>
            )}

            {activeTab === 'docs' && (
              <Card className="p-6 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]" data-rounded="default">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 font-sans">Documentation</h2>
                <div className="space-y-6">
                  {/* Getting Started */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 font-sans">Getting Started</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Quick Start Guide</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">Learn the basics of Xact Data and get up and running in minutes.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Read Guide
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Account Setup</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">Configure your account settings and connect your TikTok Shop.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Setup Guide
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Features */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 font-sans">Features</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Product Analytics</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">Track winning products and analyze performance metrics.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Learn More
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Creator Tracking</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">Monitor competitors and top-performing creators.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Learn More
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">AI Insights</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">Get AI-powered recommendations and growth strategies.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Learn More
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* API & Integrations */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 font-sans">API & Integrations</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">API Documentation</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">Integrate Xact Data with your existing tools and workflows.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          View API Docs
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">TikTok Shop Integration</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">Connect your TikTok Shop account for real-time data sync.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Integration Guide
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'videos' && (
              <Card className="p-6 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]" data-rounded="default">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 font-sans">Video Tutorials</h2>
                <div className="space-y-6">
                  {/* Getting Started Videos */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 font-sans">Getting Started</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-3 flex items-center justify-center">
                          <PlayIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Platform Overview</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">5 min • Introduction to Xact Data's main features and dashboard.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Watch Video
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-3 flex items-center justify-center">
                          <PlayIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">First Time Setup</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">8 min • Step-by-step account setup and TikTok Shop connection.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Watch Video
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Feature Tutorials */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 font-sans">Feature Tutorials</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-3 flex items-center justify-center">
                          <PlayIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Product Research</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">12 min • Find winning products using our analytics tools.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Watch Video
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-3 flex items-center justify-center">
                          <PlayIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Competitor Analysis</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">15 min • Track and analyze your competitors effectively.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Watch Video
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-3 flex items-center justify-center">
                          <PlayIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">AI Growth Coach</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">10 min • Leverage AI insights for business growth.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Watch Video
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Advanced Tutorials */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 font-sans">Advanced Tutorials</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-3 flex items-center justify-center">
                          <PlayIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">Advanced Analytics</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">20 min • Deep dive into advanced analytics and reporting features.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Watch Video
                        </Button>
                      </div>
                      <div className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A] rounded-lg hover:shadow-md transition-shadow">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg mb-3 flex items-center justify-center">
                          <PlayIcon className="w-12 h-12 text-gray-400 dark:text-gray-500" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">API Integration</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">25 min • Build custom integrations using our API.</p>
                        <Button
                          intent="gray"
                          variant="outlined"
                          size="sm"
                          className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none"
                        >
                          Watch Video
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}


          </div>
        </div>
      </div>
    </AppLayout>
  );
}