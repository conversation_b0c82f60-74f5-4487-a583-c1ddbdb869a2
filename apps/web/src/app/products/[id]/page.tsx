'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { AppLayout } from '../../../components/layout/AppLayout';
import { Card } from '../../../components/ui/Card';
import Button from '../../../components/ui/Button';
import Badge from '../../../components/ui/Badge';
import { LoadingSpinner } from '../../../components/LoadingSpinner';
import { Product, ApiResponse } from '@xact-data/shared';
import { createApiUrl } from '../../../lib/api';
import {
  TrendingUpIcon,
  ArrowLeftIcon,
  Users2Icon,
  DollarSignIcon,
  PackageIcon,
  StarIcon,
  VideoIcon,
  MessageSquareIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  PlayCircleIcon,
  Sparkles as SparklesIcon,
} from 'lucide-react';

interface ProductProfileData extends Product {
  aiAnalysis?: {
    objections: Array<{
      objection: string;
      handling: string;
      priority: 'high' | 'medium' | 'low';
    }>;
    videoFormats: {
      shortForm: Array<{
        format: string;
        description: string;
        bestFor: string;
      }>;
      longForm: Array<{
        format: string;
        description: string;
        bestFor: string;
      }>;
    };
    livestreamFormats: Array<{
      format: string;
      description: string;
      timing: string;
    }>;
    messaging: {
      hooks: string[];
      callToActions: string[];
      keyPoints: string[];
    };
  };
  topCreators?: Array<{
    creator: {
      id: string;
      username: string;
      displayName?: string;
      followerCount: number;
      profileImageUrl?: string;
    };
    gmv: number;
    sales: number;
  }>;
}

export default function ProductProfilePage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.id as string;
  
  const [product, setProduct] = useState<ProductProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'objections' | 'video' | 'livestream' | 'messaging'>('overview');

  useEffect(() => {
    fetchProductProfile();
  }, [productId]);

  const fetchProductProfile = async () => {
    try {
      setLoading(true);
      // Fetch AI-generated analysis from the API
      const response = await fetch(createApiUrl(`api/products/${productId}/ai-analysis`));
      const data: ApiResponse<{ product: Product; aiAnalysis: ProductProfileData['aiAnalysis'] }> = await response.json();

      if (data.success && data.data) {
        setProduct({ ...data.data.product, aiAnalysis: data.data.aiAnalysis });
      } else {
        setError(data.error || 'Failed to fetch product');
      }
    } catch (err) {
      setError('Failed to connect to the server');
      console.error('Error fetching product:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    );
  }

  if (error || !product) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Card className="p-8 text-center">
            <div className="text-red-500 text-xl mb-4">Error loading product</div>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button
              intent="primary"
              onClick={() => router.push('/products')}
              data-rounded="default"
            >
              Back to Products
            </Button>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <Button
            intent="gray"
            variant="outlined"
            size="sm"
            onClick={() => router.push('/products')}
            className="mb-4 border border-gray-300 dark:border-gray-600"
            data-rounded="default"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Products
          </Button>
          <h1 className="text-3xl font-bold text-black dark:text-white" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Product Profile
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            AI-powered insights and recommendations for selling this product
          </p>
        </div>

        {/* Product Overview */}
        <Card className="p-6 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Product Image */}
            <div className="flex-shrink-0">
              {product.imageUrl ? (
                <img
                  src={product.imageUrl}
                  alt={product.title}
                  className="w-48 h-48 object-cover rounded-lg border border-gray-200 dark:border-[#2A2A2A]"
                />
              ) : (
                <div className="w-48 h-48 bg-gray-100 dark:bg-[#1A1A1A] rounded-lg flex items-center justify-center border border-gray-200 dark:border-[#2A2A2A]">
                  <PackageIcon className="w-16 h-16 text-gray-400" />
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="flex-1 space-y-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 font-sans">
                  {product.title}
                </h2>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="soft" intent="primary">{product.category}</Badge>
                  <Badge variant="soft" intent="success">
                    <TrendingUpIcon className="w-3 h-3 mr-1" />
                    {product.trendScore.toFixed(0)} Trend Score
                  </Badge>
                  {product.productRating && (
                    <Badge variant="soft" intent="warning">
                      <StarIcon className="w-3 h-3 mr-1" />
                      {product.productRating.toFixed(1)}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="p-3 bg-gray-50 dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-[#2A2A2A]">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-1 font-sans">Price</div>
                  <div className="text-xl font-bold text-gray-900 dark:text-white font-sans">${product.price.toFixed(2)}</div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-[#2A2A2A]">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-1 font-sans">Commission</div>
                  <div className="text-xl font-bold text-green-600 dark:text-green-400 font-sans">{product.commissionRate.toFixed(1)}%</div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-[#2A2A2A]">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-1 font-sans">24h Sales</div>
                  <div className="text-xl font-bold text-gray-900 dark:text-white font-sans">{product.soldIn24h.toLocaleString()}</div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-[#2A2A2A]">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-1 font-sans">Creators</div>
                  <div className="text-xl font-bold text-gray-900 dark:text-white font-sans">{product.creatorsCarrying.toLocaleString()}</div>
                </div>
              </div>

              {/* Get Link Button */}
              <Button
                intent="primary"
                size="lg"
                className="w-full md:w-auto"
                onClick={() => window.open(product.affiliateLink || `https://tiktokshop.com/product/${product.tiktokProductId}`, '_blank')}
                data-rounded="default"
              >
                <DollarSignIcon className="w-4 h-4 mr-2" />
                Get Affiliate Link
              </Button>
            </div>
          </div>
        </Card>

        {/* AI Analysis Tabs */}
        <Card className="p-0 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-[#2A2A2A]">
            <div className="flex overflow-x-auto">
              <button
                onClick={() => setActiveTab('overview')}
                className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors border-b-2 whitespace-nowrap font-sans ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <SparklesIcon className="w-4 h-4" />
                AI Overview
              </button>
              <button
                onClick={() => setActiveTab('objections')}
                className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors border-b-2 whitespace-nowrap font-sans ${
                  activeTab === 'objections'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <AlertCircleIcon className="w-4 h-4" />
                Objections & Handling
              </button>
              <button
                onClick={() => setActiveTab('video')}
                className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors border-b-2 whitespace-nowrap font-sans ${
                  activeTab === 'video'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <VideoIcon className="w-4 h-4" />
                Video Formats
              </button>
              <button
                onClick={() => setActiveTab('livestream')}
                className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors border-b-2 whitespace-nowrap font-sans ${
                  activeTab === 'livestream'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <PlayCircleIcon className="w-4 h-4" />
                Livestream Formats
              </button>
              <button
                onClick={() => setActiveTab('messaging')}
                className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors border-b-2 whitespace-nowrap font-sans ${
                  activeTab === 'messaging'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <MessageSquareIcon className="w-4 h-4" />
                Messaging
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && product.aiAnalysis && (
              <div className="space-y-6">
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                  <div className="flex items-start gap-3">
                    <SparklesIcon className="w-6 h-6 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                    <div>
                      <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2 font-sans">AI-Powered Insights</h3>
                      <p className="text-blue-800 dark:text-blue-300 font-sans">
                        Our AI has analyzed this product's performance, market trends, and creator strategies to provide you with 
                        actionable insights for maximizing your sales and commissions.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="p-6 border border-gray-200 dark:border-[#2A2A2A]">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3 font-sans">Key Selling Points</h4>
                    <ul className="space-y-2">
                      {product.aiAnalysis.messaging.keyPoints.slice(0, 4).map((point, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                          <span className="text-sm text-gray-700 dark:text-gray-300 font-sans">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </Card>

                  <Card className="p-6 border border-gray-200 dark:border-[#2A2A2A]">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3 font-sans">Top Hooks</h4>
                    <ul className="space-y-2">
                      {product.aiAnalysis.messaging.hooks.slice(0, 3).map((hook, index) => (
                        <li key={index} className="text-sm text-gray-700 dark:text-gray-300 p-3 bg-gray-50 dark:bg-[#1A1A1A] rounded-lg font-sans">
                          {hook}
                        </li>
                      ))}
                    </ul>
                  </Card>
                </div>
              </div>
            )}

            {activeTab === 'objections' && product.aiAnalysis && (
              <div className="space-y-4">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 font-sans">Common Objections & How to Handle Them</h3>
                  <p className="text-gray-600 dark:text-gray-400 font-sans">
                    Prepare for these common customer objections with proven handling strategies.
                  </p>
                </div>
                {product.aiAnalysis.objections.map((obj, index) => (
                  <Card key={index} className={`p-6 border ${
                    obj.priority === 'high' 
                      ? 'border-red-200 dark:border-red-900/50 bg-red-50 dark:bg-red-900/10'
                      : obj.priority === 'medium'
                      ? 'border-yellow-200 dark:border-yellow-900/50 bg-yellow-50 dark:bg-yellow-900/10'
                      : 'border-gray-200 dark:border-[#2A2A2A]'
                  }`}>
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-gray-900 dark:text-white font-sans">{obj.objection}</h4>
                      <Badge 
                        variant="soft" 
                        intent={obj.priority === 'high' ? 'danger' : obj.priority === 'medium' ? 'warning' : 'secondary'}
                        size="sm"
                      >
                        {obj.priority} priority
                      </Badge>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <p className="text-gray-700 dark:text-gray-300 font-sans">{obj.handling}</p>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {activeTab === 'video' && product.aiAnalysis && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-sans">Short-Form Video Formats</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {product.aiAnalysis.videoFormats.shortForm.map((format, index) => (
                      <Card key={index} className="p-6 border border-gray-200 dark:border-[#2A2A2A]">
                        <div className="flex items-start gap-3 mb-3">
                          <VideoIcon className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
                          <h4 className="font-semibold text-gray-900 dark:text-white font-sans">{format.format}</h4>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">{format.description}</p>
                        <div className="pt-3 border-t border-gray-200 dark:border-[#2A2A2A]">
                          <p className="text-xs text-gray-500 dark:text-gray-500 font-sans">
                            <strong>Best for:</strong> {format.bestFor}
                          </p>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-sans">Long-Form Video Formats</h3>
                  <div className="space-y-4">
                    {product.aiAnalysis.videoFormats.longForm.map((format, index) => (
                      <Card key={index} className="p-6 border border-gray-200 dark:border-[#2A2A2A]">
                        <div className="flex items-start gap-3 mb-3">
                          <VideoIcon className="w-5 h-5 text-purple-500 flex-shrink-0 mt-0.5" />
                          <h4 className="font-semibold text-gray-900 dark:text-white font-sans">{format.format}</h4>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-sans">{format.description}</p>
                        <div className="pt-3 border-t border-gray-200 dark:border-[#2A2A2A]">
                          <p className="text-xs text-gray-500 dark:text-gray-500 font-sans">
                            <strong>Best for:</strong> {format.bestFor}
                          </p>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'livestream' && product.aiAnalysis && (
              <div className="space-y-4">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 font-sans">Livestream Formats</h3>
                  <p className="text-gray-600 dark:text-gray-400 font-sans">
                    Proven livestream formats to maximize engagement and conversions.
                  </p>
                </div>
                {product.aiAnalysis.livestreamFormats.map((format, index) => (
                  <Card key={index} className="p-6 border border-gray-200 dark:border-[#2A2A2A]">
                    <div className="flex items-start gap-3 mb-3">
                      <PlayCircleIcon className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5" />
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-2 font-sans">{format.format}</h4>
                        <p className="text-gray-700 dark:text-gray-300 mb-3 font-sans">{format.description}</p>
                        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                          <Badge variant="soft" intent="secondary" size="sm">
                            ⏱️ {format.timing}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {activeTab === 'messaging' && product.aiAnalysis && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-sans">Hooks That Convert</h3>
                  <div className="space-y-3">
                    {product.aiAnalysis.messaging.hooks.map((hook, index) => (
                      <Card key={index} className="p-4 border border-gray-200 dark:border-[#2A2A2A] bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
                        <p className="text-gray-900 dark:text-white font-medium font-sans">{hook}</p>
                      </Card>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-sans">Call-to-Actions</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {product.aiAnalysis.messaging.callToActions.map((cta, index) => (
                      <Card key={index} className="p-4 border border-gray-200 dark:border-[#2A2A2A]">
                        <p className="text-gray-700 dark:text-gray-300 font-sans">{cta}</p>
                      </Card>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 font-sans">Key Talking Points</h3>
                  <div className="space-y-2">
                    {product.aiAnalysis.messaging.keyPoints.map((point, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-[#1A1A1A] rounded-lg">
                        <CheckCircleIcon className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <p className="text-gray-700 dark:text-gray-300 font-sans">{point}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      </div>
    </AppLayout>
  );
}
