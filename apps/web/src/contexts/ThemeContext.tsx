'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setThemeState] = useState<Theme>('light');
  const [mounted, setMounted] = useState(false);

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as Theme;
    if (savedTheme) {
      setThemeState(savedTheme);
    } else {
      // Default to light mode instead of system preference
      setThemeState('light');
    }
    setMounted(true);
  }, []);

  // Apply theme to document
  useEffect(() => {
    if (mounted) {
      const root = document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(theme);
      localStorage.setItem('theme', theme);

      // Apply custom dark theme colors
      if (theme === 'dark') {
        root.style.setProperty('--dark-bg', '#1A1A1A');
        root.style.setProperty('--dark-content', '#0D0D0D');
        root.style.setProperty('--dark-border', '#2A2A2A');
        root.style.setProperty('--dark-text', '#FFFFFF');
        root.style.setProperty('--dark-text-secondary', '#B3B3B3');
        root.style.setProperty('--dark-text-muted', '#808080');
        root.style.setProperty('--tooltip-bg', '#2A2A2A');
        root.style.setProperty('--tooltip-border', '#3A3A3A');
        root.style.setProperty('--tooltip-text', '#FFFFFF');
      } else {
        root.style.removeProperty('--dark-bg');
        root.style.removeProperty('--dark-content');
        root.style.removeProperty('--dark-border');
        root.style.removeProperty('--dark-text');
        root.style.removeProperty('--dark-text-secondary');
        root.style.removeProperty('--dark-text-muted');
        root.style.removeProperty('--tooltip-bg');
        root.style.removeProperty('--tooltip-border');
        root.style.removeProperty('--tooltip-text');
      }
    }
  }, [theme, mounted]);

  const toggleTheme = () => {
    setThemeState(prev => prev === 'light' ? 'dark' : 'light');
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
