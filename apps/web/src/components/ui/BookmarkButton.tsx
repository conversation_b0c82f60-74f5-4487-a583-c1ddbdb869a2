'use client';

import { useState, useEffect } from 'react';
import { BookmarkIcon } from 'lucide-react';
import Button from './Button';
import { apiGet, apiPost, apiDelete } from '../../lib/api';
import { useToast } from './Toast';

interface BookmarkButtonProps {
  creatorId: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'text';
  className?: string;
}

export function BookmarkButton({ 
  creatorId, 
  size = 'sm', 
  variant = 'icon',
  className = '' 
}: BookmarkButtonProps) {
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [loading, setLoading] = useState(false);
  const { addToast } = useToast();

  useEffect(() => {
    checkBookmarkStatus();
  }, [creatorId]);

  const checkBookmarkStatus = async () => {
    try {
      const response = await apiGet(`api/bookmarks/check/${creatorId}`);
      if (response.ok) {
        const data = await response.json();
        setIsBookmarked(data.data.isBookmarked);
      }
    } catch (error) {
      console.error('Failed to check bookmark status:', error);
    }
  };

  const handleBookmarkToggle = async () => {
    setLoading(true);
    
    try {
      if (isBookmarked) {
        const response = await apiDelete(`api/bookmarks/${creatorId}`);
        if (response.ok) {
          setIsBookmarked(false);
          addToast({
            title: 'Bookmark Removed',
            message: 'Creator removed from bookmarks',
            type: 'success'
          });
        }
      } else {
        const response = await apiPost('api/bookmarks', {
          creatorId
        });
        if (response.ok) {
          setIsBookmarked(true);
          addToast({
            title: 'Creator Bookmarked',
            message: 'Creator added to your bookmarks',
            type: 'success'
          });
        }
      }
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      addToast({
        title: 'Error',
        message: 'Failed to update bookmark',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  if (variant === 'icon') {
    return (
      <Button
        intent="gray"
        variant="outlined"
        size={size}
        onClick={handleBookmarkToggle}
        disabled={loading}
        className={`flex items-center gap-2 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none ${className}`}
      >
        <BookmarkIcon 
          className={`w-4 h-4 ${isBookmarked ? 'fill-current' : ''}`} 
        />
      </Button>
    );
  }

  return (
    <Button
      intent="gray"
      variant="outlined"
      size={size}
      onClick={handleBookmarkToggle}
      disabled={loading}
      className={`flex items-center gap-2 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A] shadow-none ${className}`}
    >
      <BookmarkIcon 
        className={`w-4 h-4 ${isBookmarked ? 'fill-current' : ''}`} 
      />
      {isBookmarked ? 'Bookmarked' : 'Bookmark'}
    </Button>
  );
}
