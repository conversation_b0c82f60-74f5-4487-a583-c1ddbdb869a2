'use client';

import { useState, useRef, useEffect } from 'react';
import { Info, X } from 'lucide-react';
import Button from './Button';
import { cn } from '../../lib/utils';

interface InfoButtonProps {
  title: string;
  description: string;
  className?: string;
}

export function InfoButton({ title, description, className }: InfoButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLButtonElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && triggerRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const popoverWidth = 320; // max-w-xs equivalent
      const popoverHeight = 120; // approximate height
      
      let x = triggerRect.left + triggerRect.width / 2 - popoverWidth / 2;
      let y = triggerRect.bottom + 8;

      // Adjust if popover would go off screen
      if (x < 8) x = 8;
      if (x + popoverWidth > window.innerWidth - 8) {
        x = window.innerWidth - popoverWidth - 8;
      }
      
      if (y + popoverHeight > window.innerHeight - 8) {
        y = triggerRect.top - popoverHeight - 8;
      }

      setPosition({ x, y });
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen]);

  return (
    <>
      <button
        ref={triggerRef}
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "inline-flex items-center justify-center w-5 h-5 rounded-full",
          "text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300",
          "hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1",
          className
        )}
        aria-label="More information"
      >
        <Info className="w-3 h-3" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 z-40" />
          
          {/* Popover */}
          <div
            ref={popoverRef}
            className="fixed z-50 max-w-xs bg-white dark:bg-[#1A1A1A] border border-gray-200 dark:border-[#2A2A2A] rounded-lg shadow-lg p-4 animate-in fade-in-0 zoom-in-95 duration-200"
            style={{
              left: position.x,
              top: position.y,
              fontFamily: 'General Sans, system-ui, sans-serif'
            }}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 pr-2">
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                  {title}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {description}
                </p>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-md text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                aria-label="Close"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Arrow */}
            <div className="absolute -top-1 left-1/2 transform -translate-x-1/2">
              <div className="w-2 h-2 bg-white dark:bg-[#1A1A1A] border-l border-t border-gray-200 dark:border-[#2A2A2A] rotate-45"></div>
            </div>
          </div>
        </>
      )}
    </>
  );
}
