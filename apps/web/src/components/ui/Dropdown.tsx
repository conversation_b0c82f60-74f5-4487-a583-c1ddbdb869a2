'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon } from 'lucide-react';
import { cn } from '../../lib/utils';
import Button from './Button';

interface DropdownOption {
  value: string;
  label: string;
  description?: string;
}

interface DropdownProps {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function Dropdown({
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  className = '',
  disabled = false
}: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = options.find(option => option.value === value);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleOptionClick = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      <Button
        variant="outlined"
        intent="gray"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className="w-full justify-between"
        data-rounded="large"
      >
        <span className="truncate">
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <ChevronDownIcon 
          className={cn(
            'h-4 w-4 transition-transform duration-200',
            isOpen ? 'rotate-180' : ''
          )} 
        />
      </Button>

      {isOpen && (
        <div className="absolute z-50 mt-2 w-full bg-white dark:bg-[#1A1A1A] border border-gray-200 dark:border-[#2A2A2A] rounded-xl shadow-lg max-h-60 overflow-auto">
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => handleOptionClick(option.value)}
              className={cn(
                'w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-[#2A2A2A] transition-colors',
                'first:rounded-t-xl last:rounded-b-xl',
                value === option.value ? 'bg-primary-50 dark:bg-[#2A2A2A] text-primary-700 dark:text-primary-400' : 'text-gray-900 dark:text-gray-300'
              )}
            >
              <div className="font-medium">{option.label}</div>
              {option.description && (
                <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">{option.description}</div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

export default Dropdown;
