'use client';

import {
  MagicWand01 as MagicWandIcon,
  TrendUp01 as TrendingUpIcon,
  <PERSON><PERSON><PERSON><PERSON>gle as <PERSON><PERSON>TriangleIcon,
  CheckCircle as CheckCircleIcon
} from '@untitled-ui/icons-react';
import { Card } from '../ui/Card';

interface CoachCommentaryProps {
  type?: 'default' | 'success' | 'warning' | 'info';
  title?: string;
  message: string;
  actionable?: boolean;
  className?: string;
}

export function CoachCommentary({ 
  type = 'default', 
  title = 'Coach Commentary',
  message, 
  actionable = false,
  className = ''
}: CoachCommentaryProps) {
  const getThemeClasses = () => {
    switch (type) {
      case 'success':
        return 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800';
      case 'warning':
        return 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border-yellow-200 dark:border-yellow-800';
      case 'info':
        return 'bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800';
      default:
        return 'bg-gradient-to-r from-gray-50 to-gray-100 dark:from-[#0D0D0D] dark:to-[#1A1A1A] border-gray-200 dark:border-[#2A2A2A]';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="w-6 h-6 text-green-600 dark:text-green-400" />;
      case 'warning':
        return <AlertTriangleIcon className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />;
      case 'info':
        return <TrendingUpIcon className="w-6 h-6 text-purple-600 dark:text-purple-400" />;
      default:
        return <MagicWandIcon className="w-6 h-6 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getIconBgColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-100 dark:bg-green-900/30';
      case 'warning':
        return 'bg-yellow-100 dark:bg-yellow-900/30';
      case 'info':
        return 'bg-purple-100 dark:bg-purple-900/30';
      default:
        return 'bg-gray-100 dark:bg-[#2A2A2A]';
    }
  };

  return (
    <Card className={`p-6 ${getThemeClasses()} ${className}`} data-rounded="default">
      <div className="flex items-start gap-4">
        <div className={`w-12 h-12 ${getIconBgColor()} rounded-full flex items-center justify-center`}>
          {getIcon()}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            {title}
          </h3>
          <p className="text-gray-700 dark:text-gray-300" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            {message}
          </p>
          {actionable && (
            <div className="mt-3 flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 font-medium">
              <CheckCircleIcon className="w-4 h-4" />
              <span style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                Click the cards below to take action
              </span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
