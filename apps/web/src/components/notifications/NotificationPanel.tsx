'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useNotifications } from '../../contexts/NotificationContext';
import { Card } from '../ui/Card';
import Button from '../ui/Button';
import { Badge } from '../ui/Badge';
import {
  XClose as XIcon,
  CheckCircle as CheckIcon,
  Target04 as TargetIcon
} from '@untitled-ui/icons-react';

export function NotificationPanel() {
  const router = useRouter();
  const {
    notifications,
    isOpen,
    closePanel,
    markAsRead,
    markAllAsRead,
    unreadCount
  } = useNotifications();

  const [activeTab, setActiveTab] = useState<'current' | 'history'>('current');

  if (!isOpen) return null;

  // Filter notifications based on active tab
  const currentNotifications = notifications.filter(n => !n.isRead);
  const historyNotifications = notifications.filter(n => n.isRead);
  const displayNotifications = activeTab === 'current' ? currentNotifications : historyNotifications;





  const handleNotificationClick = (notification: any) => {
    markAsRead(notification.id);

    if (notification.type === 'product_alert') {
      // Navigate to products page
      router.push('/products');
      closePanel();
    } else if (notification.type === 'competitor_alert') {
      // Navigate to competitors page
      router.push('/competitors');
      closePanel();
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor(diff / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ago`;
    } else if (minutes > 0) {
      return `${minutes}m ago`;
    } else {
      return 'Just now';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-end p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/40 animate-in fade-in duration-200"
        onClick={closePanel}
      />

      {/* Panel */}
      <Card className="relative w-full max-w-sm h-[500px] bg-white dark:bg-[#1A1A1A] shadow-2xl border border-gray-200 dark:border-[#2A2A2A] flex flex-col animate-in slide-in-from-right-4 duration-300">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-[#2A2A2A]">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white font-sans">Alerts</h2>
            <Button intent="gray" variant="ghost" size="sm" onClick={closePanel} className="p-2 h-8 w-8 hover:bg-gray-100 dark:hover:bg-[#2A2A2A]">
              <XIcon className="w-4 h-4" />
            </Button>
          </div>

          {/* Tab Switcher */}
          <div className="flex bg-gray-100 dark:bg-[#2A2A2A] rounded-lg p-1">
            <button
              onClick={() => setActiveTab('current')}
              className={`flex-1 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                activeTab === 'current'
                  ? 'bg-white dark:bg-[#3A3A3A] text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Current
              {currentNotifications.length > 0 && (
                <span className="ml-1 px-1.5 py-0.5 bg-gray-100 dark:bg-[#2A2A2A] text-gray-700 dark:text-gray-300 text-xs rounded-full">
                  {currentNotifications.length}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`flex-1 px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                activeTab === 'history'
                  ? 'bg-white dark:bg-[#3A3A3A] text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              History
            </button>
          </div>
        </div>

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {displayNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-6">
              <div className="w-12 h-12 bg-gray-100 dark:bg-[#2A2A2A] rounded-full flex items-center justify-center mb-3">
                <TargetIcon className="w-6 h-6 text-gray-400 dark:text-gray-500" />
              </div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white font-sans mb-1">
                {activeTab === 'current' ? 'No active alerts' : 'No alert history'}
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 font-sans">
                {activeTab === 'current' ? 'All alerts are resolved' : 'No previous alerts found'}
              </p>
            </div>
          ) : (
            <div className="p-2">
              {displayNotifications.map((notification, index) => (
                <div
                  key={notification.id}
                  className={`mx-2 mb-2 p-3 rounded-lg border transition-all cursor-pointer ${
                    notification.type === 'product_alert'
                      ? 'border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/20 hover:bg-green-50 dark:hover:bg-green-900/30'
                      : notification.type === 'competitor_alert'
                      ? 'border-gray-200 dark:border-[#2A2A2A] bg-gray-50/50 dark:bg-[#2A2A2A]/50 hover:bg-gray-50 dark:hover:bg-[#2A2A2A]'
                      : 'border-gray-200 dark:border-[#2A2A2A] bg-gray-50/50 dark:bg-[#2A2A2A]/50 hover:bg-gray-50 dark:hover:bg-[#2A2A2A]'
                  }`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start gap-3">
                    {/* Alert Status Indicator */}
                    <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                      notification.type === 'product_alert'
                        ? 'bg-green-500 dark:bg-green-400'
                        : notification.type === 'competitor_alert'
                        ? 'bg-gray-500 dark:bg-gray-400'
                        : 'bg-gray-500 dark:bg-gray-400'
                    }`} />

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <h4 className="text-sm font-semibold text-gray-900 dark:text-white font-sans">
                            Alert Triggered
                          </h4>
                          <p className="text-sm text-gray-700 dark:text-gray-300 font-sans mt-1">
                            {notification.title}
                          </p>
                          <p className="text-xs text-gray-600 dark:text-gray-400 font-sans mt-1">
                            {notification.message}
                          </p>
                        </div>

                        {/* Alert Badge and Time */}
                        <div className="flex flex-col items-end gap-2">
                          {notification.metadata?.trendScore && (
                            <Badge variant="soft" intent="success" size="sm" className="text-xs">
                              {notification.metadata.trendScore}%
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Time at bottom */}
                      <div className="mt-3 pt-2 border-t border-gray-100 dark:border-[#2A2A2A]">
                        <span className="text-xs text-gray-400 dark:text-gray-500 font-sans">
                          {formatTimestamp(notification.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        {activeTab === 'current' && currentNotifications.length > 0 && (
          <div className="p-3 border-t border-gray-200">
            <Button
              intent="primary"
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="w-full justify-center text-xs h-7 text-gray-600 hover:text-gray-900"
            >
              <CheckIcon className="w-3 h-3 mr-1" />
              Mark All as Read
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}
