'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card } from '../ui/Card';
import Button from '../ui/Button';
import { Badge } from '../ui/Badge';
import { useToast } from '../ui/Toast';
import { 
  X as XIcon, 
  ExternalLink as ExternalLinkIcon, 
  RefreshCw as RefreshCwIcon,
  CheckCircle as CheckCircleIcon,
  AlertCircle as AlertCircleIcon
} from 'lucide-react';
import Image from 'next/image';

interface TikTokConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ConnectionStatus {
  isConnected: boolean;
  isTokenExpired: boolean;
  shopName?: string;
  connectedAt?: string;
  needsReconnection: boolean;
}

export function TikTokConnectionModal({ isOpen, onClose }: TikTokConnectionModalProps) {
  const { data: session } = useSession();
  const { addToast } = useToast();
  const [connecting, setConnecting] = useState(false);
  const [status, setStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isTokenExpired: false,
    needsReconnection: false,
  });
  const [loading, setLoading] = useState(true);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  useEffect(() => {
    if (isOpen && session?.user?.id) {
      checkConnectionStatus();
    } else if (isOpen) {
      setLoading(false);
    }
  }, [isOpen, session?.user?.id]);

  const checkConnectionStatus = async () => {
    if (!session?.user?.id) return;

    try {
      setLoading(true);
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/status/${session.user.id}?t=${Date.now()}`);
      const data = await response.json();
      
      if (data.success) {
        const mappedStatus: ConnectionStatus = {
          isConnected: data.data.isConnected,
          isTokenExpired: data.data.tokenExpired,
          shopName: data.data.shopInfo?.shopName,
          connectedAt: data.data.shopInfo?.connectedAt,
          needsReconnection: data.data.tokenExpired || false,
        };
        setStatus(mappedStatus);
      } else {
        setStatus({
          isConnected: false,
          isTokenExpired: false,
          needsReconnection: false,
        });
      }
    } catch (error) {
      console.error('Failed to check TikTok Shop connection status:', error);
      setStatus({
        isConnected: false,
        isTokenExpired: false,
        needsReconnection: false,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    if (!session?.user?.id) {
      addToast({
        type: 'error',
        title: 'Authentication Required',
        message: 'Please sign in to connect your TikTok Shop account.'
      });
      return;
    }

    setConnecting(true);
    
    try {
      const redirectUri = `${window.location.origin}/auth/tiktok-shop/callback`;
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/auth-url?userId=${session.user.id}&redirectUri=${encodeURIComponent(redirectUri)}`);

      const data = await response.json();
      
      if (data.success) {
        window.location.href = data.data.authUrl;
      } else {
        addToast({
          type: 'error',
          title: 'Connection Failed',
          message: data.message || 'Failed to generate authorization URL'
        });
      }
    } catch (error) {
      console.error('Failed to initiate TikTok Shop connection:', error);
      addToast({
        type: 'error',
        title: 'Connection Error',
        message: 'Failed to connect to TikTok Shop. Please try again.'
      });
    } finally {
      setConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!session?.user?.id) return;

    setConnecting(true);
    
    try {
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/disconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.id,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setStatus({
          isConnected: false,
          isTokenExpired: false,
          needsReconnection: false,
        });
        addToast({
          type: 'success',
          title: 'Disconnected Successfully',
          message: 'Your TikTok Shop account has been disconnected.'
        });
      } else {
        addToast({
          type: 'error',
          title: 'Disconnection Failed',
          message: data.message || 'Failed to disconnect TikTok Shop account'
        });
      }
    } catch (error) {
      console.error('Failed to disconnect TikTok Shop:', error);
      addToast({
        type: 'error',
        title: 'Disconnection Error',
        message: 'Failed to disconnect TikTok Shop. Please try again.'
      });
    } finally {
      setConnecting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-md shadow-2xl border border-gray-200" data-rounded="default">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
              <Image
                src="/tiktok.png"
                alt="TikTok"
                width={20}
                height={20}
                className="object-contain"
              />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 font-sans">TikTok Shop Connection</h2>
              <p className="text-sm text-gray-500 font-sans">Manage your affiliate account connection</p>
            </div>
          </div>
          <Button intent="gray" variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
            <XIcon className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCwIcon className="w-6 h-6 animate-spin text-gray-400" />
              <span className="ml-2 text-sm text-gray-600 font-sans">Checking connection status...</span>
            </div>
          ) : (
            <>
              {/* Connection Status */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {status.isConnected && !status.needsReconnection ? (
                      <CheckCircleIcon className="w-5 h-5 text-green-500" />
                    ) : status.needsReconnection ? (
                      <AlertCircleIcon className="w-5 h-5 text-yellow-500" />
                    ) : (
                      <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-900 font-sans">
                        {status.isConnected && !status.needsReconnection
                          ? 'Connected'
                          : status.needsReconnection
                          ? 'Needs Reconnection'
                          : 'Not Connected'
                        }
                      </p>
                      {status.shopName && (
                        <p className="text-xs text-gray-600 font-sans">{status.shopName}</p>
                      )}
                    </div>
                  </div>
                </div>
                
                {status.isConnected && !status.needsReconnection ? (
                  <Badge variant="soft" intent="success" size="sm">Connected</Badge>
                ) : status.needsReconnection ? (
                  <Badge variant="soft" intent="warning" size="sm">Expired</Badge>
                ) : (
                  <Badge variant="soft" intent="gray" size="sm">Disconnected</Badge>
                )}
              </div>

              {/* Benefits */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-900 font-sans">What you'll get:</h3>
                <ul className="text-sm text-gray-600 font-sans space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Real-time commission tracking
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Product performance analytics
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    AI-powered growth insights
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Automated data synchronization
                  </li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                {status.isConnected && !status.needsReconnection ? (
                  <>
                    <Button
                      onClick={checkConnectionStatus}
                      intent="gray"
                      variant="outlined"
                      size="sm"
                      disabled={connecting}
                      className="flex items-center gap-2 border border-gray-300 text-gray-700 hover:text-gray-900 hover:border-gray-400 bg-white shadow-sm"
                    >
                      <RefreshCwIcon className={`w-4 h-4 ${connecting ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                    <Button
                      onClick={handleDisconnect}
                      intent="gray"
                      variant="outlined"
                      size="sm"
                      disabled={connecting}
                      className="flex items-center gap-2 border border-red-300 text-red-600 hover:text-red-700 hover:border-red-400 bg-white shadow-sm"
                    >
                      Disconnect
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={handleConnect}
                    disabled={connecting}
                    intent="gray"
                    variant="outlined"
                    size="md"
                    className="flex items-center gap-2 w-full justify-center border border-gray-300 text-gray-700 hover:text-gray-900 hover:border-gray-400 bg-white shadow-sm"
                  >
                    {connecting ? (
                      <>
                        <RefreshCwIcon className="w-4 h-4 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      <>
                        <ExternalLinkIcon className="w-4 h-4" />
                        {status.needsReconnection ? 'Reconnect Account' : 'Connect TikTok Shop'}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </>
          )}
        </div>
      </Card>
    </div>
  );
}
