'use client';

import React, { useState } from 'react';
import Button from '../ui/Button';
import Image from 'next/image';
import { TikTokConnectionModal } from './TikTokConnectionModal';

export function TikTokConnectionButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        intent="gray"
        variant="outlined"
        size="sm"
        onClick={() => setIsModalOpen(true)}
        className="flex items-center gap-2 border border-gray-300 dark:border-[#3A3A3A] text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-[#4A4A4A] bg-white dark:bg-[#1A1A1A] hover:bg-gray-50 dark:hover:bg-[#2A2A2A] shadow-sm transition-all duration-200"
      >
        <div className="w-4 h-4 flex items-center justify-center">
          <Image
            src="/tiktok.png"
            alt="TikTok"
            width={16}
            height={16}
            className="object-contain"
          />
        </div>
        Connect TikTok
      </Button>

      <TikTokConnectionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}
