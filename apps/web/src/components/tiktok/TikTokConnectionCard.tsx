'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card } from '../ui/Card';
import Button from '../ui/Button';
import { useToast } from '../ui/Toast';
import { X as XIcon, ExternalLink as ExternalLinkIcon, RefreshCw as RefreshCwIcon } from 'lucide-react';
import Image from 'next/image';

interface TikTokConnectionCardProps {
  onDismiss: () => void;
}

interface ConnectionStatus {
  isConnected: boolean;
  isTokenExpired: boolean;
  shopName?: string;
  connectedAt?: string;
  needsReconnection: boolean;
}

export function TikTokConnectionCard({ onDismiss }: TikTokConnectionCardProps) {
  const { data: session } = useSession();
  const { addToast } = useToast();
  const [connecting, setConnecting] = useState(false);
  const [status, setStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isTokenExpired: false,
    needsReconnection: false,
  });
  const [loading, setLoading] = useState(true);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  useEffect(() => {
    if (session?.user?.id) {
      checkConnectionStatus();
    }
  }, [session?.user?.id]);

  const checkConnectionStatus = async () => {
    if (!session?.user?.id) return;

    try {
      setLoading(true);
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/status/${session.user.id}?t=${Date.now()}`);
      const data = await response.json();
      
      if (data.success) {
        const mappedStatus: ConnectionStatus = {
          isConnected: data.data.isConnected,
          isTokenExpired: data.data.tokenExpired,
          shopName: data.data.shopInfo?.shopName,
          connectedAt: data.data.shopInfo?.connectedAt,
          needsReconnection: data.data.tokenExpired || false,
        };
        setStatus(mappedStatus);
      }
    } catch (error) {
      console.error('Failed to check TikTok Shop connection status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    if (!session?.user?.id) return;

    setConnecting(true);
    
    try {
      const redirectUri = `${window.location.origin}/auth/tiktok-shop/callback`;
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/auth-url?userId=${session.user.id}&redirectUri=${encodeURIComponent(redirectUri)}`);

      const data = await response.json();
      
      if (data.success) {
        window.location.href = data.data.authUrl;
      } else {
        addToast({
          type: 'error',
          title: 'Connection Failed',
          message: data.message || 'Failed to generate authorization URL'
        });
      }
    } catch (error) {
      console.error('Failed to initiate TikTok Shop connection:', error);
      addToast({
        type: 'error',
        title: 'Connection Error',
        message: 'Failed to connect to TikTok Shop. Please try again.'
      });
    } finally {
      setConnecting(false);
    }
  };

  // Don't show the card if already connected
  if (status.isConnected && !status.needsReconnection) {
    return null;
  }

  // Don't show while loading
  if (loading) {
    return null;
  }

  return (
    <Card className="p-6 mb-4 relative overflow-hidden border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#0D0D0D]" data-rounded="default">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gray-100/30 dark:bg-gray-700/20 rounded-full blur-xl"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-gray-100/20 dark:bg-gray-700/10 rounded-full blur-lg"></div>
      
      {/* Close button */}
      <button
        onClick={onDismiss}
        className="absolute top-4 right-4 p-1 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-[#2A2A2A]"
        aria-label="Dismiss"
      >
        <XIcon className="w-5 h-5" />
      </button>

      <div className="flex items-start gap-4 pr-8">
        {/* TikTok Logo */}
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-gray-50 dark:bg-[#1A1A1A] rounded-lg border border-gray-200 dark:border-[#2A2A2A] flex items-center justify-center shadow-sm">
            <Image
              src="/tiktok.png"
              alt="TikTok"
              width={32}
              height={32}
              className="object-contain"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1">
          <div className="mb-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-sans">
              Connect Your TikTok Shop Account
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-sans mt-1">
              {status.needsReconnection
                ? 'Your TikTok Shop connection needs to be refreshed to continue syncing your affiliate performance data.'
                : 'Sync your TikTok Shop affiliate performance data to get real-time insights and track your earnings.'
              }
            </p>
          </div>

          {/* Benefits */}
          <div className="mb-4">
            <ul className="text-sm text-gray-600 dark:text-gray-400 font-sans space-y-1">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
                Real-time commission tracking
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
                Product performance analytics
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
                AI-powered growth insights
              </li>
            </ul>
          </div>

          {/* Action Button */}
          <Button
            onClick={handleConnect}
            disabled={connecting}
            intent="gray"
            variant="outlined"
            size="md"
            className="flex items-center gap-2 border border-gray-300 dark:border-[#3A3A3A] text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-[#4A4A4A] bg-white dark:bg-[#1A1A1A] hover:bg-gray-50 dark:hover:bg-[#2A2A2A] shadow-sm transition-all duration-200"
          >
            {connecting ? (
              <>
                <RefreshCwIcon className="w-4 h-4 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                <ExternalLinkIcon className="w-4 h-4" />
                {status.needsReconnection ? 'Reconnect Account' : 'Connect TikTok Shop'}
              </>
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
}
