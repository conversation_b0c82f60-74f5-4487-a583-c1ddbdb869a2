'use client';

import { useState, useEffect } from 'react';

interface FilterProps {
  category: string;
  minTrendScore: number;
  minGMV: number;
  maxGMV: number;
  sortBy: 'trendScore' | 'soldIn24h' | 'estimatedGMV' | 'commissionRate';
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

interface ProductFiltersProps {
  filters: FilterProps;
  onFilterChange: (filters: Partial<FilterProps>) => void;
}



const gmvRanges = [
  { id: 'any', label: 'Any GMV', min: 0, max: 0 },
  { id: 'low', label: '$0-250K', min: 0, max: 250000 },
  { id: 'medium', label: '$250K-500K', min: 250000, max: 500000 },
  { id: 'high', label: '$500K-1M', min: 500000, max: 1000000 },
  { id: 'very-high', label: '$1M-5M', min: 1000000, max: 5000000 },
  { id: 'ultra-high', label: '$5M+', min: 5000000, max: 0 }
];

export function ProductFilters({ filters, onFilterChange }: ProductFiltersProps) {
  // Determine current GMV range based on filters
  const getCurrentGMVRange = () => {
    const range = gmvRanges.find(r =>
      r.min === filters.minGMV && (r.max === filters.maxGMV || (r.max === 0 && filters.maxGMV === 0))
    );
    return range?.id || 'any';
  };

  const [selectedGMVRange, setSelectedGMVRange] = useState(getCurrentGMVRange());

  // Sync local state with filters when they change
  useEffect(() => {
    setSelectedGMVRange(getCurrentGMVRange());
  }, [filters.minGMV, filters.maxGMV]);

  const handleGMVRangeChange = (rangeId: string) => {
    const range = gmvRanges.find(r => r.id === rangeId);
    if (range) {
      setSelectedGMVRange(rangeId);
      onFilterChange({
        minGMV: range.min,
        maxGMV: range.max
      });
    }
  };

  const clearFilters = () => {
    setSelectedGMVRange('any');
    onFilterChange({
      minGMV: 0,
      maxGMV: 0,
    });
  };

  const hasActiveFilters = selectedGMVRange !== 'any';

  return (
    <div className="flex items-center justify-between gap-6">
      {/* GMV Range Filter */}
      <div className="flex items-center gap-3">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 font-sans">GMV Range:</label>
        <div className="flex space-x-1 bg-gray-100 dark:bg-[#2A2A2A] p-1 rounded-lg">
          {gmvRanges.map(range => (
            <button
              key={range.id}
              onClick={() => handleGMVRangeChange(range.id)}
              className={`
                px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                ${selectedGMVRange === range.id
                  ? 'bg-white dark:bg-[#1A1A1A] text-gray-900 dark:text-white shadow-sm border border-gray-200 dark:border-[#3A3A3A]'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-[#1A1A1A]/50'
                }
              `}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>

      {/* Clear Filters */}
      {hasActiveFilters && (
        <button
          onClick={clearFilters}
          className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors font-sans"
        >
          Clear filters
        </button>
      )}
    </div>
  );
}