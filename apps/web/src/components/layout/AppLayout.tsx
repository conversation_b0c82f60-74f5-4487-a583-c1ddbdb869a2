'use client';

import { ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { usePathname } from 'next/navigation';
import {
  SearchSm as SearchIcon,
  Menu01 as MenuIcon,
  ChevronRight as ChevronRightIcon
} from '@untitled-ui/icons-react';
import Button from '../ui/Button';
import { AppSidebar } from './AppSidebar';
import { WhopLoginButton } from '../auth/WhopLoginButton';
import { NoSSR } from '../NoSSR';
import { NotificationBell } from '../notifications/NotificationBell';
import { NotificationPanel } from '../notifications/NotificationPanel';
import { BookmarkedCreators } from '../BookmarkedCreators';

interface AppLayoutProps {
  children: ReactNode;
}

const pathNames: Record<string, string> = {
  '/dashboard': 'Dashboard',
  '/products': 'Products',
  '/alerts': 'Alerts',
  '/analytics': 'Analytics',
  '/competitors': 'Creators',
  '/settings': 'Settings',

  '/insights': 'AI Growth Coach',
  '/action-plans': 'Daily Action Plans',
  '/benchmarks': 'Competitive Benchmarks',
  '/top-products': 'Top Products',
};

export function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();

  return (
    <NoSSR 
      fallback={
        <div className="flex h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        </div>
      }
    >
      <AppLayoutContent pathname={pathname}>
        {children}
      </AppLayoutContent>
    </NoSSR>
  );
}

function AppLayoutContent({ children, pathname }: { children: ReactNode; pathname: string }) {
  const { data: session, status } = useSession();

  // Bypass authentication in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';





  if (status === 'loading' && !isDevelopment) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // In development, skip authentication check and show the app directly
  // In production, require authentication
  if (!session && !isDevelopment) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <div className="mx-auto flex h-16 w-16 items-center justify-center mb-6">
              <img
                src="/09png.png"
                alt="Xact Data Logo"
                className="object-contain"
                style={{ width: '64px', height: 'auto', maxHeight: '64px' }}
              />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white font-sans">Welcome to Xact Data</h2>
            <p className="mt-2 text-gray-600 dark:text-gray-300 font-sans">Sign in to access your Creator OS dashboard</p>
          </div>
          <div className="flex justify-center">
            <WhopLoginButton />
          </div>
        </div>
      </div>
    );
  }

  const currentPageName = pathNames[pathname] || 'Dashboard';

  return (
    <div className="flex h-screen bg-white dark:bg-[#1A1A1A] overflow-hidden">
      {/* Sidebar - Fixed position */}
      <AppSidebar />

      {/* Main Content Area - Scrollable */}
      <div className="flex flex-1 flex-col bg-white dark:bg-[#1A1A1A] overflow-hidden">
        {/* Page Content - Stacked on top design */}
        <main className="flex-1 bg-white dark:bg-[#1A1A1A] p-6 overflow-auto">
          <div className="min-h-full bg-gray-50 dark:bg-[#0D0D0D] rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-[#2A2A2A]">
            {/* Page Header */}
            <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200 dark:border-[#2A2A2A]">
              <div className="flex items-center gap-4">
                {/* Mobile menu button */}
                <Button
                  intent="gray"
                  variant="ghost"
                  size="sm"
                  className="md:hidden"
                  data-rounded="default"
                >
                  <MenuIcon className="h-5 w-5" />
                </Button>

                {/* Breadcrumbs */}
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Dashboard</span>
                  {pathname !== '/dashboard' && (
                    <>
                      <ChevronRightIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                      <span className="font-medium text-gray-900 dark:text-gray-100">{currentPageName}</span>
                    </>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-4">
                {/* Global Search */}
                <div className="relative hidden md:block">
                  <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 dark:text-gray-500" />
                  <input
                    type="text"
                    placeholder="Search products, competitors..."
                    className="w-64 rounded-lg border border-gray-300 dark:border-[#2A2A2A] bg-white dark:bg-[#2A2A2A] py-2 pl-10 pr-4 text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-gray-400 dark:focus:border-gray-500 focus:outline-none focus:ring-1 focus:ring-gray-400 dark:focus:ring-gray-500"
                    data-rounded="default"
                  />
                </div>

                {/* Notifications */}
                <NotificationBell />
              </div>
            </div>

            {/* Page Content */}
            <div className="pb-6">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* Notification Panel */}
      <NotificationPanel />

      {/* Bookmarks Widget */}
      <BookmarkedCreators variant="compact" />
    </div>
  );
}