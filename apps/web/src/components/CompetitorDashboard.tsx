'use client';

import { useState, useEffect } from 'react';
import { CompetitorList } from './CompetitorList';
import { AddCompetitorModal } from './AddCompetitorModal';
import { CompetitorProfile } from './CompetitorProfile';
import { CompetitorAnalysis } from './CompetitorAnalysis';
import { CompetitorPlaybooks } from './CompetitorPlaybooks';
import { CompetitorBenchmarks } from './CompetitorBenchmarks';
import { PlusIcon } from 'lucide-react';
import { Competitor } from '@xact-data/shared';
import { apiGet, apiPost, apiDelete } from '../lib/api';
import Button from './ui/Button';

// Using shared Competitor type from @xact-data/shared

export function CompetitorDashboard() {
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [selectedCompetitor, setSelectedCompetitor] = useState<Competitor | null>(null);
  const [activeView, setActiveView] = useState<'list' | 'profile' | 'analysis' | 'playbooks' | 'benchmarks'>('list');
  const [filterCategory, setFilterCategory] = useState<'all' | 'beauty' | 'fashion' | 'tech' | 'lifestyle'>('all');
  const [sortBy, setSortBy] = useState<'followers' | 'engagement' | 'gmv' | 'growth'>('followers');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCompetitors();
  }, []);

  const fetchCompetitors = async () => {
    try {
      setLoading(true);
      const response = await apiGet('api/competitors');
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(data.data.competitors || []);
      }
    } catch (error) {
      console.error('Failed to fetch competitors:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCompetitor = async (username: string, nickname?: string) => {
    try {
      const response = await apiPost('api/competitors', { username, nickname });
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(prev => [data.data, ...prev]);
        setIsAddModalOpen(false);
      } else {
        alert(data.error || 'Failed to add competitor');
      }
    } catch (error) {
      console.error('Failed to add competitor:', error);
      alert('Failed to add competitor');
    }
  };

  const handleRemoveCompetitor = async (competitorId: string) => {
    if (!confirm('Are you sure you want to remove this competitor?')) return;

    try {
      const response = await apiDelete(`api/competitors/${competitorId}`);
      const data = await response.json();
      
      if (data.success) {
        setCompetitors(prev => prev.filter(c => c.id !== competitorId));
        if (selectedCompetitor?.id === competitorId) {
          setSelectedCompetitor(null);
          setActiveView('list');
        }
      } else {
        alert(data.error || 'Failed to remove competitor');
      }
    } catch (error) {
      console.error('Failed to remove competitor:', error);
      alert('Failed to remove competitor');
    }
  };

  const handleCompetitorSelect = (competitor: Competitor) => {
    setSelectedCompetitor(competitor);
    setActiveView('profile');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Button - Only show when not in list view */}
      {activeView !== 'list' && selectedCompetitor && (
        <div className="flex items-center gap-3">
          <Button
            intent="gray"
            variant="outlined"
            size="sm"
            onClick={() => setActiveView('list')}
            className="flex items-center gap-2 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-[#1A1A1A]"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Creators
          </Button>
        </div>
      )}

      {/* Navigation - Only show when in list view */}
      {activeView === 'list' && (
        <div className="flex items-center justify-between">
          <div className="px-4 py-2 text-lg font-semibold text-gray-900 dark:text-white" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Tracked Creators ({competitors.length})
          </div>

          {/* Add Competitor Button */}
          <Button
            intent="gray"
            variant="outlined"
            size="sm"
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 border-gray-300 text-gray-600 hover:text-gray-900 hover:border-gray-400"
          >
            <PlusIcon className="w-4 h-4" />
            Add Creator
          </Button>
        </div>
      )}

      {/* Filters */}
      {activeView === 'list' && (
        <div className="flex items-center gap-4">
          <div className="flex space-x-1 bg-gray-100 dark:bg-[#2A2A2A] p-1 rounded-lg">
            {['all', 'beauty', 'fashion', 'tech', 'lifestyle'].map((category) => (
              <button
                key={category}
                onClick={() => setFilterCategory(category as any)}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  filterCategory === category
                    ? 'bg-white dark:bg-[#1A1A1A] text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-[#1A1A1A]/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
          <div className="flex space-x-1 bg-gray-100 dark:bg-[#2A2A2A] p-1 rounded-lg">
            {[
              { key: 'followers', label: 'Followers' },
              { key: 'engagement', label: 'Engagement' },
              { key: 'gmv', label: 'GMV' },
              { key: 'growth', label: 'Growth' }
            ].map((sort) => (
              <button
                key={sort.key}
                onClick={() => setSortBy(sort.key as any)}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                  sortBy === sort.key
                    ? 'bg-white dark:bg-[#1A1A1A] text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-[#1A1A1A]/50'
                }`}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                {sort.label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Main Content */}
      {activeView === 'list' && (
        <CompetitorList
          competitors={competitors}
          onCompetitorSelect={handleCompetitorSelect}
          onCompetitorRemove={handleRemoveCompetitor}
          onRefreshCompetitors={fetchCompetitors}
          onAddCompetitor={() => setIsAddModalOpen(true)}
        />
      )}



      {activeView === 'profile' && selectedCompetitor && (
        <CompetitorProfile
          competitor={selectedCompetitor}
        />
      )}

      {activeView === 'analysis' && selectedCompetitor && (
        <CompetitorAnalysis
          competitor={selectedCompetitor}
          onBack={() => setActiveView('profile')}
        />
      )}

      {activeView === 'playbooks' && selectedCompetitor && (
        <CompetitorPlaybooks
          competitor={selectedCompetitor}
          onBack={() => setActiveView('profile')}
        />
      )}

      {activeView === 'benchmarks' && selectedCompetitor && (
        <CompetitorBenchmarks
          competitor={selectedCompetitor}
          onBack={() => setActiveView('profile')}
        />
      )}

      {/* Add Competitor Modal */}
      <AddCompetitorModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddCompetitor}
      />
    </div>
  );
}