'use client';

import { useState, useEffect } from 'react';
import { Card } from './ui/Card';
import { BookmarkButton } from './ui/BookmarkButton';
import { 
  UserIcon, 
  TrendingUpIcon, 
  EyeIcon,
  BookmarkIcon
} from 'lucide-react';
import { apiGet } from '../lib/api';

interface BookmarkedCreator {
  id: string;
  createdAt: string;
  creator: {
    id: string;
    username: string;
    displayName?: string;
    followerCount: number;
    engagementRate: number;
    totalGMV?: number;
    profileImageUrl?: string;
    creatorProducts?: Array<{
      product: {
        id: string;
        name: string;
        price: number;
      };
      gmv: number;
    }>;
  };
}

interface BookmarkedCreatorsProps {
  onCreatorSelect?: (creator: any) => void;
  variant?: 'full' | 'compact';
}

export function BookmarkedCreators({ onCreatorSelect, variant = 'full' }: BookmarkedCreatorsProps) {
  const [bookmarkedCreators, setBookmarkedCreators] = useState<BookmarkedCreator[]>([]);
  const [loading, setLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    fetchBookmarkedCreators();
  }, []);

  const fetchBookmarkedCreators = async () => {
    try {
      const response = await apiGet('api/bookmarks');
      if (response.ok) {
        const data = await response.json();
        setBookmarkedCreators(data.data.bookmarks);
      }
    } catch (error) {
      console.error('Failed to fetch bookmarked creators:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getEngagementColor = (rate: number) => {
    if (rate >= 5) return 'bg-green-100 text-green-800';
    if (rate >= 3) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  // Compact variant for corner widget
  if (variant === 'compact') {
    if (loading) {
      return (
        <div className="fixed bottom-6 right-6 z-40">
          <Card className="p-4 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A] shadow-lg" data-rounded="default">
            <div className="flex items-center gap-2">
              <BookmarkIcon className="w-5 h-5 text-gray-600 dark:text-gray-400 animate-pulse" />
              <span className="text-sm font-medium text-gray-900 dark:text-white font-sans">Loading...</span>
            </div>
          </Card>
        </div>
      );
    }

    if (bookmarkedCreators.length === 0) {
      return null; // Don't show widget if no bookmarks
    }

    return (
      <div className="fixed bottom-6 right-6 z-40">
        <div className="relative">
          {/* Compact Widget */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-3 p-4 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A] rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 font-sans"
          >
            <BookmarkIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <div className="text-left">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                Bookmarked Creators
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {bookmarkedCreators.length} saved
              </div>
            </div>
          </button>

          {/* Expanded Panel */}
          {isExpanded && (
            <div className="absolute bottom-full right-0 mb-2 w-80 max-h-96 overflow-y-auto">
              <Card className="p-4 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A] shadow-xl" data-rounded="default">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white font-sans">
                    Bookmarked Creators ({bookmarkedCreators.length})
                  </h3>
                  <button
                    onClick={() => setIsExpanded(false)}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    ×
                  </button>
                </div>

                <div className="space-y-3">
                  {bookmarkedCreators.slice(0, 5).map((bookmark) => (
                    <div key={bookmark.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-[#1A1A1A] transition-colors">
                      <div className="relative">
                        {bookmark.creator.profileImageUrl ? (
                          <img
                            src={bookmark.creator.profileImageUrl}
                            alt={bookmark.creator.username}
                            className="h-8 w-8 rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <UserIcon className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 dark:text-white font-sans truncate">
                          {bookmark.creator.displayName || bookmark.creator.username}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 font-sans">
                          {formatNumber(bookmark.creator.followerCount || 0)} followers
                        </div>
                      </div>

                      {onCreatorSelect && (
                        <button
                          onClick={() => onCreatorSelect(bookmark.creator)}
                          className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-sans"
                        >
                          View
                        </button>
                      )}
                    </div>
                  ))}

                  {bookmarkedCreators.length > 5 && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 text-center pt-2 border-t border-gray-200 dark:border-[#2A2A2A] font-sans">
                      +{bookmarkedCreators.length - 5} more
                    </div>
                  )}
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Full variant (original design)
  if (loading) {
    return (
      <Card className="p-6 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]" data-rounded="default">
        <div className="flex items-center gap-3 mb-4">
          <BookmarkIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-sans">
            Bookmarked Creators
          </h3>
        </div>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  if (bookmarkedCreators.length === 0) {
    return (
      <Card className="p-6 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]" data-rounded="default">
        <div className="flex items-center gap-3 mb-4">
          <BookmarkIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-sans">
            Bookmarked Creators
          </h3>
        </div>
        <div className="text-center py-8">
          <BookmarkIcon className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400 font-sans">
            No bookmarked creators yet. Start bookmarking creators to see them here.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 bg-white dark:bg-[#0D0D0D] border border-gray-200 dark:border-[#2A2A2A]" data-rounded="default">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <BookmarkIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white font-sans">
            Bookmarked Creators ({bookmarkedCreators.length})
          </h3>
        </div>
      </div>

      <div className="space-y-4">
        {bookmarkedCreators.map((bookmark) => (
          <div key={bookmark.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-[#2A2A2A] rounded-lg hover:bg-gray-50 dark:hover:bg-[#1A1A1A] transition-colors">
            <div className="flex items-center space-x-4">
              <div className="relative">
                {bookmark.creator.profileImageUrl ? (
                  <img
                    src={bookmark.creator.profileImageUrl}
                    alt={bookmark.creator.username}
                    className="h-12 w-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                    <UserIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                  </div>
                )}
              </div>

              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium text-gray-900 dark:text-white font-sans">
                    {bookmark.creator.displayName || bookmark.creator.username}
                  </h4>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-sans">@{bookmark.creator.username}</p>
                
                {/* Stats */}
                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <UserIcon className="h-4 w-4 mr-1" />
                    {formatNumber(bookmark.creator.followerCount || 0)} followers
                  </div>
                  <div className={`flex items-center text-sm px-2 py-1 rounded ${getEngagementColor(bookmark.creator.engagementRate || 0)}`}>
                    <TrendingUpIcon className="h-4 w-4 mr-1" />
                    <span>{(bookmark.creator.engagementRate || 0).toFixed(1)}%</span>
                  </div>
                  {bookmark.creator.totalGMV && bookmark.creator.totalGMV > 0 && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <span className="font-medium">${formatNumber(bookmark.creator.totalGMV || 0)} GMV</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-3">
              <BookmarkButton 
                creatorId={bookmark.creator.id}
                variant="icon"
                size="sm"
              />
              {onCreatorSelect && (
                <button
                  onClick={() => onCreatorSelect(bookmark.creator)}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-[#1A1A1A] border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-[#2A2A2A] transition-colors font-sans"
                  data-rounded="default"
                >
                  <EyeIcon className="h-4 w-4 mr-2" />
                  View
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}
