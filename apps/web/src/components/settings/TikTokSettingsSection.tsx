'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card } from '../ui/Card';
import Button from '../ui/Button';
import { Badge } from '../ui/Badge';
import { useToast } from '../ui/Toast';
import { TikTokConnectionModal } from '../tiktok/TikTokConnectionModal';
import { 
  ExternalLink as ExternalLinkIcon, 
  RefreshCw as RefreshCwIcon,
  CheckCircle as CheckCircleIcon,
  AlertCircle as AlertCircleIcon,
  Settings as SettingsIcon
} from 'lucide-react';
import Image from 'next/image';

interface ConnectionStatus {
  isConnected: boolean;
  isTokenExpired: boolean;
  shopName?: string;
  connectedAt?: string;
  needsReconnection: boolean;
}

export function TikTokSettingsSection() {
  const { data: session } = useSession();
  const { addToast } = useToast();
  const [showModal, setShowModal] = useState(false);
  const [status, setStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isTokenExpired: false,
    needsReconnection: false,
  });
  const [loading, setLoading] = useState(true);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

  useEffect(() => {
    if (session?.user?.id) {
      checkConnectionStatus();
    } else {
      setLoading(false);
    }
  }, [session?.user?.id]);

  const checkConnectionStatus = async () => {
    if (!session?.user?.id) return;

    try {
      setLoading(true);
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/status/${session.user.id}?t=${Date.now()}`);
      const data = await response.json();
      
      if (data.success) {
        const mappedStatus: ConnectionStatus = {
          isConnected: data.data.isConnected,
          isTokenExpired: data.data.tokenExpired,
          shopName: data.data.shopInfo?.shopName,
          connectedAt: data.data.shopInfo?.connectedAt,
          needsReconnection: data.data.tokenExpired || false,
        };
        setStatus(mappedStatus);
      } else {
        setStatus({
          isConnected: false,
          isTokenExpired: false,
          needsReconnection: false,
        });
      }
    } catch (error) {
      console.error('Failed to check TikTok Shop connection status:', error);
      setStatus({
        isConnected: false,
        isTokenExpired: false,
        needsReconnection: false,
      });
    } finally {
      setLoading(false);
    }
  };

  if (!session?.user?.id) {
    return (
      <div className="p-4 border border-gray-200 rounded-lg" data-rounded="default">
        <p className="text-gray-500 font-sans">Please sign in to manage your integrations.</p>
      </div>
    );
  }

  return (
    <>
      <Card className="p-6 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700" data-rounded="default">
        <div className="flex items-start gap-4">
          {/* TikTok Logo */}
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gray-50 dark:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-500 flex items-center justify-center">
              <Image
                src="/tiktok.png"
                alt="TikTok"
                width={24}
                height={24}
                className="object-contain"
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 font-sans">TikTok Shop</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 font-sans mt-1">
                  Connect your TikTok Shop account to sync affiliate performance data
                </p>
                {status.shopName && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-sans mt-1">
                    Connected as: {status.shopName}
                  </p>
                )}
                {status.connectedAt && (
                  <p className="text-xs text-gray-400 dark:text-gray-500 font-sans mt-1">
                    Connected on {new Date(status.connectedAt).toLocaleDateString()}
                  </p>
                )}
              </div>

              {/* Status Badge */}
              <div className="flex items-center gap-3">
                {loading ? (
                  <RefreshCwIcon className="w-5 h-5 animate-spin text-gray-400" />
                ) : status.isConnected && !status.needsReconnection ? (
                  <Badge variant="soft" intent="success" className="flex items-center gap-1">
                    <CheckCircleIcon className="w-3 h-3" />
                    Connected
                  </Badge>
                ) : status.needsReconnection ? (
                  <Badge variant="soft" intent="warning" className="flex items-center gap-1">
                    <AlertCircleIcon className="w-3 h-3" />
                    Needs Reconnection
                  </Badge>
                ) : (
                  <Badge variant="soft" intent="gray">Not Connected</Badge>
                )}
              </div>
            </div>

            {/* Benefits */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 font-sans mb-2">Benefits:</h4>
              <ul className="text-sm text-gray-600 dark:text-gray-400 font-sans space-y-1">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 dark:bg-blue-400 rounded-full"></div>
                  Real-time commission tracking
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 dark:bg-blue-400 rounded-full"></div>
                  Product performance analytics
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 dark:bg-blue-400 rounded-full"></div>
                  AI-powered growth insights
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 mt-4">
              <Button
                onClick={() => setShowModal(true)}
                intent="gray"
                variant="outlined"
                size="sm"
                className="flex items-center gap-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-600 shadow-sm"
              >
                <SettingsIcon className="w-4 h-4" />
                {status.isConnected ? 'Manage Connection' : 'Connect Account'}
              </Button>

              {status.isConnected && (
                <Button
                  onClick={checkConnectionStatus}
                  intent="gray"
                  variant="ghost"
                  size="sm"
                  disabled={loading}
                  className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                >
                  <RefreshCwIcon className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Modal */}
      <TikTokConnectionModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      />
    </>
  );
}
