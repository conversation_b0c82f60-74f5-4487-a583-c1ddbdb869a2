'use client';

import { useState } from 'react';
import {
  UserIcon,
  EyeIcon,
  TrendingUpIcon,
  BellIcon,
  RefreshCwIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExternalLinkIcon
} from 'lucide-react';
import { Competitor } from '@xact-data/shared';
import { apiPost } from '../lib/api';
import { BookmarkButton } from './ui/BookmarkButton';

// Using shared Competitor type from @xact-data/shared

interface CompetitorListProps {
  competitors: Competitor[];
  onCompetitorSelect: (competitor: Competitor) => void;
  onCompetitorRemove: (competitorId: string) => void;
  onRefreshCompetitors: () => void;
  onAddCompetitor: () => void;
}

export function CompetitorList({
  competitors,
  onCompetitorSelect,
  onCompetitorRemove,
  onRefreshCompetitors,
  onAddCompetitor
}: CompetitorListProps) {
  const [refreshingIds, setRefreshingIds] = useState<Set<string>>(new Set());

  const handleRefreshCompetitor = async (competitorId: string) => {
    setRefreshingIds(prev => new Set(prev).add(competitorId));
    
    try {
      const response = await apiPost(`api/competitors/${competitorId}/refresh`);
      
      if (response.ok) {
        onRefreshCompetitors();
      }
    } catch (error) {
      console.error('Failed to refresh competitor:', error);
    } finally {
      setRefreshingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(competitorId);
        return newSet;
      });
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getEngagementColor = (rate: number): string => {
    if (rate >= 5) return 'text-green-600 bg-green-50';
    if (rate >= 3) return 'text-yellow-600 bg-yellow-50';
    if (rate >= 1) return 'text-orange-600 bg-orange-50';
    return 'text-red-600 bg-red-50';
  };

  if (competitors.length === 0) {
    return (
      <div className="text-center py-12">
        <UserIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white font-sans">No competitors</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 font-sans">
          Get started by adding your first competitor to track.
        </p>
        <button
          onClick={onAddCompetitor}
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 dark:bg-gray-500 hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 font-sans"
          data-rounded="default"
        >
          <UserIcon className="w-4 h-4 mr-2" />
          Add Your First Creator
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-[#0D0D0D] shadow-sm rounded-lg overflow-hidden border border-gray-200 dark:border-[#2A2A2A]">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-[#2A2A2A]">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Tracked Creators ({competitors.length})
        </h3>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-[#2A2A2A]">
        {competitors.map((competitor) => (
          <div key={competitor.id} className="p-6 hover:bg-gray-50 dark:hover:bg-[#1A1A1A]">
            <div className="flex items-center justify-between">
              {/* Competitor Info */}
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {competitor.creator.profileImageUrl ? (
                    <img
                      src={competitor.creator.profileImageUrl}
                      alt={competitor.creator.username}
                      className="h-12 w-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                      <UserIcon className="h-6 w-6 text-gray-600" />
                    </div>
                  )}

                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white truncate font-sans">
                      {competitor.creator.displayName || competitor.creator.username}
                    </h4>
                    {competitor.creator.isVerified && (
                      <CheckCircleIcon className="w-4 h-4 text-blue-600" />
                    )}
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-sans">@{competitor.creator.username}</p>
                  
                  {/* Stats */}
                  <div className="flex items-center space-x-6 mt-2">
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <UserIcon className="h-4 w-4 mr-1" />
                      {formatNumber(competitor.creator.followerCount || 0)} followers
                    </div>
                    <div className={`flex items-center text-sm px-2 py-1 rounded ${getEngagementColor(competitor.creator.engagementRate || 0)} dark:bg-gray-800/50`}>
                      <TrendingUpIcon className="h-4 w-4 mr-1" />
                      <span className="text-gray-700 dark:text-gray-300">{(competitor.creator.engagementRate || 0).toFixed(1)}% engagement</span>
                    </div>
                    {competitor.creator.totalGMV && competitor.creator.totalGMV > 0 && (
                      <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <span className="font-medium">${formatNumber(competitor.creator.totalGMV || 0)} GMV</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-3">
                {competitor.creator.id && (
                  <BookmarkButton
                    creatorId={competitor.creator.id}
                    variant="icon"
                    size="sm"
                  />
                )}
                <button
                  onClick={() => onCompetitorSelect(competitor)}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-all duration-200 font-sans"
                  data-rounded="default"
                >
                  <EyeIcon className="h-4 w-4 mr-2" />
                  View Profile
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}