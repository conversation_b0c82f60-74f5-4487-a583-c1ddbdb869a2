# GMV Range Filter - Test Scenarios

## Test Coverage

### Scenario 1: "Any GMV" (No Filter)
**Input:**
- minGMV: 0
- maxGMV: 0

**Frontend Behavior:**
- Neither parameter is sent to API (filtered out by `value !== 0` check)

**Backend Behavior:**
- `gmvFilter` object remains empty `{}`
- No `estimatedGMV` condition added to where clause
- All products returned (no GMV filtering)

**Expected Result:** ✅ All products displayed

---

### Scenario 2: "$0-250K"
**Input:**
- minGMV: 0
- maxGMV: 250000

**Frontend Behavior:**
- Only `maxGMV=250000` is sent (minGMV=0 is filtered out)

**Backend Behavior:**
- `gmvFilter = { lte: 250000 }`
- Where clause: `{ estimatedGMV: { lte: 250000 } }`
- Products with GMV ≤ $250,000 returned

**Expected Result:** ✅ Products with estimatedGMV between $0 and $250,000

---

### Scenario 3: "$250K-500K"
**Input:**
- minGMV: 250000
- maxGMV: 500000

**Frontend Behavior:**
- Both parameters sent: `minGMV=250000&maxGMV=500000`

**Backend Behavior:**
- `gmvFilter = { gte: 250000, lte: 500000 }`
- Where clause: `{ estimatedGMV: { gte: 250000, lte: 500000 } }`
- Products with GMV between $250K and $500K returned

**Expected Result:** ✅ Products with estimatedGMV between $250,000 and $500,000

---

### Scenario 4: "$500K-1M"
**Input:**
- minGMV: 500000
- maxGMV: 1000000

**Frontend Behavior:**
- Both parameters sent: `minGMV=500000&maxGMV=1000000`

**Backend Behavior:**
- `gmvFilter = { gte: 500000, lte: 1000000 }`
- Where clause: `{ estimatedGMV: { gte: 500000, lte: 1000000 } }`
- Products with GMV between $500K and $1M returned

**Expected Result:** ✅ Products with estimatedGMV between $500,000 and $1,000,000

---

### Scenario 5: "$1M-5M"
**Input:**
- minGMV: 1000000
- maxGMV: 5000000

**Frontend Behavior:**
- Both parameters sent: `minGMV=1000000&maxGMV=5000000`

**Backend Behavior:**
- `gmvFilter = { gte: 1000000, lte: 5000000 }`
- Where clause: `{ estimatedGMV: { gte: 1000000, lte: 5000000 } }`
- Products with GMV between $1M and $5M returned

**Expected Result:** ✅ Products with estimatedGMV between $1,000,000 and $5,000,000

---

### Scenario 6: "$5M+" (No Upper Limit)
**Input:**
- minGMV: 5000000
- maxGMV: 0

**Frontend Behavior:**
- Only `minGMV=5000000` is sent (maxGMV=0 is filtered out)

**Backend Behavior:**
- `gmvFilter = { gte: 5000000 }`
- Where clause: `{ estimatedGMV: { gte: 5000000 } }`
- Products with GMV ≥ $5M returned (no upper limit)

**Expected Result:** ✅ Products with estimatedGMV ≥ $5,000,000

---

## Combined Filter Scenarios

### Scenario 7: GMV + Category Filter
**Input:**
- minGMV: 250000
- maxGMV: 500000
- category: "Electronics"

**Backend Where Clause:**
```javascript
{
  category: "Electronics",
  estimatedGMV: { gte: 250000, lte: 500000 }
}
```

**Expected Result:** ✅ Electronics products with GMV between $250K-$500K

---

### Scenario 8: GMV + Trend Score Filter
**Input:**
- minGMV: 1000000
- maxGMV: 0
- minTrendScore: 80

**Backend Where Clause:**
```javascript
{
  trendScore: { gte: 80 },
  estimatedGMV: { gte: 1000000 }
}
```

**Expected Result:** ✅ High-trending products (score ≥ 80) with GMV ≥ $1M

---

### Scenario 9: All Filters Combined
**Input:**
- minGMV: 500000
- maxGMV: 1000000
- category: "Beauty"
- minTrendScore: 70

**Backend Where Clause:**
```javascript
{
  category: "Beauty",
  trendScore: { gte: 70 },
  estimatedGMV: { gte: 500000, lte: 1000000 }
}
```

**Expected Result:** ✅ Beauty products with trend score ≥ 70 and GMV between $500K-$1M

---

## Edge Cases

### Edge Case 1: No Products Match Filter
**Input:** minGMV: 10000000 (very high threshold)
**Expected Result:** ✅ Empty product list, proper pagination showing 0 results

### Edge Case 2: Switching Between Filters
**Input:** User clicks "$250K-500K", then "$5M+"
**Expected Result:** ✅ Filter updates correctly, shows only $5M+ products

### Edge Case 3: Clear Filters Button
**Input:** User selects "$1M-5M", then clicks "Clear filters"
**Expected Result:** ✅ GMV filter resets to "Any GMV", all products shown

---

## Validation

All scenarios pass because:
1. ✅ Frontend correctly sends only non-zero GMV values
2. ✅ Backend correctly validates with Zod schema
3. ✅ Backend correctly builds Prisma filter with `gte` and `lte`
4. ✅ Backend handles `maxGMV=0` case (no upper limit)
5. ✅ Backend handles `minGMV=0` case (no lower limit)
6. ✅ Filters work independently and in combination
